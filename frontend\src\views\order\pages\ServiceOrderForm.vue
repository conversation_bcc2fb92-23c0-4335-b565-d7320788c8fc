<template>
  <div class="service-order-form">
    <!-- 页面标题和操作栏 -->
    <div class="page-header">
      <div class="header-left">
        <el-button @click="handleBack" class="back-button">
          <el-icon><ArrowLeft /></el-icon>
          返回
        </el-button>
        <h2>{{ pageTitle }}</h2>
      </div>
      <div class="header-actions">
        <el-button v-if="mode === 'view'" type="primary" @click="handleEdit">
          编辑
        </el-button>
        <el-button
          v-if="isEditing"
          type="success"
          :loading="saving"
          @click="handleSave"
        >
          保存
        </el-button>
        <el-button v-if="isEditing" @click="handleCancel">
          取消
        </el-button>

        <!-- 审核/弃审按钮 - 只在查看模式显示 -->
        <template v-if="mode === 'view'">
          <el-button
            v-if="orderData.audit_status === '待审核'"
            type="success"
            @click="handleAudit"
            :loading="auditing"
          >
            <el-icon><Check /></el-icon>
            审核
          </el-button>
          <el-button
            v-if="orderData.audit_status === '已审核'"
            type="warning"
            @click="handleRevertAudit"
            :loading="auditing"
          >
            <el-icon><RefreshLeft /></el-icon>
            弃审
          </el-button>
        </template>
      </div>
    </div>

    <!-- 表单内容 -->
    <div class="form-content" v-loading="loading">
      <!-- 订单状态展示 -->
      <OrderStatusBadge
        :audit-status="orderData.audit_status"
        :payment-status="orderData.payment_status"
        :commission-status="orderData.commission_status"
        :show-payment-status="false"
      />

      <!-- 订单表头 -->
      <OrderHeader
        v-model="orderData"
        :is-editing="isEditing"
        :mode="mode"
        ref="orderHeaderRef"
      />

      <!-- 标签页内容 -->
      <div class="tabs-section">
        <el-tabs v-model="activeTab" class="form-tabs" type="border-card">
          <!-- 服务信息 -->
          <el-tab-pane name="service">
            <template #label>
              <span class="tab-label">
                <el-icon><Tools /></el-icon>
                服务信息
              </span>
            </template>
            <div class="tab-content">
              <!-- 服务明细 -->
              <ServiceInfo
                v-model="orderData"
                :is-editing="isEditing"
                ref="serviceInfoRef"
              />
            </div>
          </el-tab-pane>

          <!-- 附件管理 -->
          <el-tab-pane name="attachments">
            <template #label>
              <span class="tab-label">
                <el-icon><Paperclip /></el-icon>
                附件管理
              </span>
            </template>
            <OrderAttachment
              v-if="orderId"
              :order-id="orderId"
              :is-editing="isEditing"
              :allow-upload="true"
            />
            <div v-else class="attachment-placeholder">
              <el-empty description="请先保存订单，然后才能管理附件"></el-empty>
            </div>
          </el-tab-pane>


        </el-tabs>
      </div>

      <!-- 表尾信息 -->
      <div class="form-footer">
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="备注">
              <el-input
                v-model="orderData.remark"
                type="textarea"
                :rows="3"
                placeholder="请输入备注信息"
                :disabled="!isEditing"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="制单人">
              <el-input
                :value="orderData.creator?.name || ''"
                disabled
                placeholder="系统自动填充"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="制单时间">
              <el-input
                v-model="formattedCreatedAt"
                disabled
                placeholder="系统自动填充"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { ElMessage, ElMessageBox } from 'element-plus';
import { ArrowLeft, Tools, Paperclip, Check, RefreshLeft } from '@element-plus/icons-vue';

// 组件引入
import OrderHeader from '../components/OrderHeader.vue';
import ServiceInfo from '../components/ServiceInfo.vue';
import OrderAttachment from '../components/OrderAttachment.vue';
import OrderStatusBadge from '../components/OrderStatusBadge.vue';

// API引入
import {
  getOrderById,
  createServiceOrder,
  updateOrder
} from '@/api/order';

// Composables
import { useOrderAudit } from '../composables/useOrderAudit';

// 工具函数引入
import { formatDateTime } from '@/utils/format';

// 路由
const route = useRoute();
const router = useRouter();

// 响应式数据
const loading = ref(false);
const saving = ref(false);

// 使用审核 composable
const { auditing, auditOrder, revertAudit } = useOrderAudit();
const orderId = ref(route.params.id);
// 初始化模式函数
const initMode = () => {
  if (route.name === 'ServiceOrderCreate') {
    return 'create';
  } else if (route.query.mode) {
    return route.query.mode; // 兼容旧的查询参数方式
  } else {
    return orderId.value ? 'view' : 'create';
  }
};

// 根据路由名称和参数判断模式，立即初始化
const mode = ref(initMode());
const activeTab = ref('service');

// 表单引用
const orderHeaderRef = ref(null);
const serviceInfoRef = ref(null);

// 订单数据
const orderData = ref({
  order_id: '',
  order_category: '服务订单',
  enterprise_id: null,
  asset_id: null,
  user_id: null,
  creation_method: '手工创建',
  order_type: '普通订单',
  standard_amount: 0,
  actual_amount: 0,
  tax_amount: null,
  invoice_type: '不开票',
  payment_status: '已支付',
  payment_method: '在线',
  payment_time: null,
  is_partner_order: false,
  partner_user_id: null,
  commission_base: null,
  commission_extra: null,
  commission_amount: null,
  commission_status: '未发放',
  audit_status: '待审核',
  remark: '',
  service_items: [],
  // 添加创建者信息字段
  creator: null,
  created_at: null,
  updated_at: null
});

// 原始数据备份（用于取消编辑）
const originalData = ref({});

// 计算属性
const pageTitle = computed(() => {
  switch (mode.value) {
    case 'create':
      return '新增服务订单';
    case 'edit':
      return '编辑服务订单';
    case 'review':
      return '审核服务订单';
    case 'view':
    default:
      return '查看服务订单';
  }
});

const isEditing = computed(() => {
  return mode.value === 'create' || mode.value === 'edit' || mode.value === 'review';
});

const formattedCreatedAt = computed(() => {
  return formatDateTime(orderData.value.created_at);
});



// 监听服务明细变化，更新订单金额
watch(() => orderData.value.service_items, (newItems) => {
  if (newItems && newItems.length > 0) {
    const standardTotal = newItems.reduce((sum, item) => sum + (parseFloat(item.standard_price) || 0), 0);
    const actualTotal = newItems.reduce((sum, item) => sum + (parseFloat(item.actual_price) || 0), 0);
    
    orderData.value.standard_amount = standardTotal;
    orderData.value.actual_amount = actualTotal;
  }
}, { deep: true });

// 获取订单详情
const fetchOrderData = async () => {
  if (!orderId.value || mode.value === 'create') return;
  
  loading.value = true;
  try {
    const response = await getOrderById(orderId.value);
    orderData.value = {
      ...orderData.value,
      ...response,
      service_items: response.serviceItems || []
    };
    
    // 备份原始数据
    originalData.value = JSON.parse(JSON.stringify(orderData.value));
  } catch (error) {
    console.error('获取订单详情失败:', error);
    ElMessage.error('获取订单详情失败');
  } finally {
    loading.value = false;
  }
};

// 初始化新订单
const initNewOrder = async () => {
  if (mode.value !== 'create') return;

  // 订单号由 OrderHeader 组件自动生成，这里不需要重复调用
  console.log('初始化新服务订单');

  // 添加默认的服务明细行
  orderData.value.service_items = [{
    service_name: '',
    service_description: '',
    standard_price: 0,
    actual_price: 0,
    remark: ''
  }];
};

// 表单验证
const validateForm = async () => {
  const validations = await Promise.all([
    orderHeaderRef.value?.validate(),
    serviceInfoRef.value?.validate()
  ]);

  return validations.every(result => result);
};

// 保存订单
const handleSave = async () => {
  const isValid = await validateForm();
  if (!isValid) {
    ElMessage.error('请检查表单信息');
    return;
  }

  // 验证必要的业务字段
  if (!orderData.value.user_id && !orderData.value.enterprise_id) {
    ElMessage.error('必须选择用户或企业');
    return;
  }

  saving.value = true;
  try {
    if (mode.value === 'create') {
      // 创建新订单前，打印调试信息
      console.log('准备创建服务订单，数据:', {
        user_id: orderData.value.user_id,
        enterprise_id: orderData.value.enterprise_id,
        service_items: orderData.value.service_items,
        standard_amount: orderData.value.standard_amount,
        actual_amount: orderData.value.actual_amount
      });

      // 转换数据格式：service_items -> serviceItems
      const serviceOrderData = {
        ...orderData.value,
        serviceItems: orderData.value.service_items
      };
      delete serviceOrderData.service_items; // 删除旧字段

      const response = await createServiceOrder(serviceOrderData);
      orderId.value = response.id;
      ElMessage.success('创建成功');

      // 切换到查看模式并重新获取数据
      mode.value = 'view';
      await fetchOrderData(); // 重新获取完整的订单数据，包括订单编码
    } else {
      // 更新订单
      await updateOrder(orderId.value, orderData.value);
      ElMessage.success('保存成功');

      // 更新原始数据
      originalData.value = JSON.parse(JSON.stringify(orderData.value));

      if (mode.value === 'edit') {
        mode.value = 'view';
      }
    }
  } catch (error) {
    console.error('保存失败:', error);
    ElMessage.error(`保存失败: ${error.message || '未知错误'}`);
  } finally {
    saving.value = false;
  }
};

// 取消编辑
const handleCancel = async () => {
  if (mode.value === 'create') {
    handleBack();
    return;
  }
  
  try {
    await ElMessageBox.confirm(
      '确定要取消编辑吗？未保存的更改将丢失。',
      '确认取消',
      {
        confirmButtonText: '确定',
        cancelButtonText: '继续编辑',
        type: 'warning',
      }
    );
    
    // 恢复原始数据
    orderData.value = JSON.parse(JSON.stringify(originalData.value));
    mode.value = 'view';
  } catch (error) {
    // 用户取消
  }
};

// 编辑模式
const handleEdit = () => {
  mode.value = 'edit';
};

// 返回
const handleBack = () => {
  router.back();
};

// 审核订单
const handleAudit = async () => {
  await auditOrder(orderId.value, async () => {
    // 审核成功后刷新数据
    await fetchOrderData();
  });
};

// 弃审订单
const handleRevertAudit = async () => {
  await revertAudit(orderId.value, async () => {
    // 弃审成功后刷新数据
    await fetchOrderData();
  });
};

// 组件挂载时初始化
onMounted(async () => {
  console.log('ServiceOrderForm 组件挂载，当前模式:', mode.value, '是否编辑:', isEditing.value);
  if (mode.value === 'create') {
    await initNewOrder();
  } else {
    await fetchOrderData();
  }
});
</script>

<style scoped>
.service-order-form {
  padding: 20px;
  min-height: 100vh;
  background: #f5f7fa;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  background: white;
  padding: 16px 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.back-button {
  padding: 8px 12px;
}

.page-header h2 {
  margin: 0;
  color: #303133;
  font-size: 20px;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.form-content {
  max-width: 1200px;
  margin: 0 auto;
}

.tabs-section {
  margin-top: 20px;
}

.form-tabs {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.tab-label {
  display: flex;
  align-items: center;
  gap: 8px;
}

.tab-content {
  padding: 20px 0;
}

.attachment-placeholder {
  padding: 40px 0;
  text-align: center;
}

.form-footer {
  margin-top: 20px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  border-top: 3px solid #409eff;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .service-order-form {
    padding: 10px;
  }
  
  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .header-left {
    justify-content: center;
  }
  
  .header-actions {
    justify-content: center;
  }
}
</style>
