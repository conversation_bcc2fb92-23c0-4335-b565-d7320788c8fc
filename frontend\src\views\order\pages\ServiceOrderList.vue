<template>
  <div class="service-order-list">
    <!-- 页面标题和操作栏 -->
    <div class="page-header">
      <h2>服务订单</h2>
      <div class="header-actions">
        <el-button type="primary" @click="handleCreate">
          <el-icon><Plus /></el-icon>
          新增服务订单
        </el-button>
        <el-button @click="handleRefresh">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div>

    <!-- 筛选栏 -->
    <div class="filter-bar">
      <el-form :model="filterForm" inline>
        <el-form-item label="订单类型">
          <el-select v-model="filterForm.orderType" placeholder="全部" clearable style="width: 120px">
            <el-option label="普通订单" value="普通订单" />
            <el-option label="续费订单" value="续费订单" />
            <el-option label="变更订单" value="变更订单" />
          </el-select>
        </el-form-item>
        <el-form-item label="创建方式">
          <el-select v-model="filterForm.creationMethod" placeholder="全部" clearable style="width: 120px">
            <el-option label="手工创建" value="手工创建" />
            <el-option label="用户创建" value="用户创建" />
          </el-select>
        </el-form-item>
        <el-form-item label="服务类型">
          <el-select v-model="filterForm.serviceType" placeholder="全部" clearable style="width: 120px">
            <el-option label="实施服务" value="实施服务" />
            <el-option label="售后服务" value="售后服务" />
            <el-option label="sps服务" value="sps服务" />
          </el-select>
        </el-form-item>
        <el-form-item label="分润状态">
          <el-select v-model="filterForm.commissionStatus" placeholder="全部" clearable style="width: 120px">
            <el-option label="已发放" value="已发放" />
            <el-option label="未发放" value="未发放" />
          </el-select>
        </el-form-item>
        <el-form-item label="订单号">
          <el-input v-model="filterForm.orderId" placeholder="输入订单号搜索" style="width: 200px" clearable />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleFilter">搜索</el-button>
          <el-button @click="handleResetFilter">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-value">{{ stats.totalOrders }}</div>
              <div class="stats-label">总订单数</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-value">¥{{ stats.totalAmount }}</div>
              <div class="stats-label">总金额</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-value">{{ stats.serviceCount.implementation }}</div>
              <div class="stats-label">实施服务</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-value">¥{{ stats.commissionAmount }}</div>
              <div class="stats-label">分润金额</div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 服务订单列表表格 -->
    <div class="table-container">
      <el-table 
        :data="filteredOrders" 
        v-loading="loading"
        stripe
        border
        style="width: 100%"
        show-summary
        :summary-method="getSummaries"
      >
        <!-- 订单号 -->
        <el-table-column prop="order_id" label="订单号" width="140" fixed="left">
          <template #default="{ row }">
            <el-link type="primary" @click="handleView(row)">
              {{ row.order_id }}
            </el-link>
          </template>
        </el-table-column>

        <!-- 企业信息 -->
        <el-table-column label="企业信息" width="200">
          <template #default="{ row }">
            <div v-if="row.enterprise">
              <div class="enterprise-name">{{ row.enterprise.name }}</div>
              <div class="enterprise-id">ID: {{ row.enterprise.enterprise_id || 'N/A' }}</div>
            </div>
            <span v-else class="text-muted">未关联企业</span>
          </template>
        </el-table-column>

        <!-- 资产信息 -->
        <el-table-column label="资产信息" width="120">
          <template #default="{ row }">
            <span v-if="row.asset">{{ row.asset.asset_id }}</span>
            <span v-else class="text-muted">未关联资产</span>
          </template>
        </el-table-column>

        <!-- 用户信息 -->
        <el-table-column label="用户信息" width="150">
          <template #default="{ row }">
            <div v-if="row.user">
              <div>{{ row.user.name }}</div>
              <div class="text-muted">{{ row.user.mobile }}</div>
            </div>
            <span v-else class="text-muted">未关联用户</span>
          </template>
        </el-table-column>

        <!-- 创建方式 -->
        <el-table-column prop="creation_method" label="创建方式" width="100" />

        <!-- 订单类型 -->
        <el-table-column prop="order_type" label="订单类型" width="100" />

        <!-- 服务明细 -->
        <el-table-column label="服务明细" width="250">
          <template #default="{ row }">
            <div v-if="row.serviceItems && row.serviceItems.length > 0">
              <div v-for="item in row.serviceItems" :key="item.id" class="service-item">
                <div class="service-header">
                  <el-tag :type="getServiceTagType(item.service_name)" size="small">
                    {{ item.service_name }}
                  </el-tag>
                  <span class="service-price">¥{{ item.actual_price }}</span>
                </div>
                <div v-if="item.related_order_id" class="related-order">
                  关联订单: {{ item.related_order_id }}
                </div>
                <div v-if="item.remark" class="service-remark">
                  {{ item.remark }}
                </div>
              </div>
            </div>
            <span v-else class="text-muted">无服务明细</span>
          </template>
        </el-table-column>

        <!-- 订单金额 -->
        <el-table-column label="订单金额" width="140">
          <template #default="{ row }">
            <div>标准: ¥{{ row.standard_amount }}</div>
            <div class="actual-amount">实付: ¥{{ row.actual_amount }}</div>
          </template>
        </el-table-column>

        <!-- 合伙人信息 -->
        <el-table-column label="合伙人信息" width="150">
          <template #default="{ row }">
            <div v-if="row.is_partner_order">
              <div>ID: {{ row.partner_user_id }}</div>
              <div class="commission-info">
                基础: {{ ((row.commission_base || 0) * 100).toFixed(2) }}%
              </div>
              <div class="commission-info">
                额外: {{ ((row.commission_extra || 0) * 100).toFixed(2) }}%
              </div>
              <div class="commission-amount">
                金额: ¥{{ row.commission_amount }}
              </div>
              <el-tag
                :type="row.commission_status === '已发放' ? 'success' : 'warning'"
                size="small"
              >
                {{ row.commission_status }}
              </el-tag>
            </div>
            <span v-else class="text-muted">否</span>
          </template>
        </el-table-column>

        <!-- 制单人 -->
        <el-table-column label="制单人" width="100">
          <template #default="{ row }">
            {{ row.creator?.name || 'N/A' }}
          </template>
        </el-table-column>

        <!-- 制单时间 -->
        <el-table-column label="制单时间" width="160">
          <template #default="{ row }">
            {{ formatDateTime(row.created_at) }}
          </template>
        </el-table-column>

        <!-- 操作列 -->
        <el-table-column label="操作" width="160" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleView(row)">
              查看
            </el-button>
            <el-button
              v-if="row.audit_status === '已审核'"
              type="warning"
              size="small"
              @click="handleRevertAudit(row)"
              :loading="auditingOrderId === row.id"
            >
              弃审
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import { Plus, Refresh } from '@element-plus/icons-vue';
import { getServiceOrders } from '@/api/order';
import { formatDateTime } from '@/utils/format';

// Composables
import { useOrderAudit } from '../composables/useOrderAudit';

// 路由
const router = useRouter();

// 响应式数据
const loading = ref(false);
const orders = ref([]);
const auditingOrderId = ref(null);

// 使用审核 composable
const { revertAudit } = useOrderAudit();

// 筛选表单
const filterForm = ref({
  orderType: '',
  creationMethod: '',
  serviceType: '',
  commissionStatus: '',
  orderId: ''
});

// 计算属性：过滤后的订单列表
const filteredOrders = computed(() => {
  let filtered = orders.value;
  
  if (filterForm.value.orderType) {
    filtered = filtered.filter(order => order.order_type === filterForm.value.orderType);
  }
  
  if (filterForm.value.creationMethod) {
    filtered = filtered.filter(order => order.creation_method === filterForm.value.creationMethod);
  }
  
  if (filterForm.value.serviceType) {
    filtered = filtered.filter(order => 
      order.serviceItems && order.serviceItems.some(item => item.service_name === filterForm.value.serviceType)
    );
  }
  
  if (filterForm.value.commissionStatus) {
    filtered = filtered.filter(order => order.commission_status === filterForm.value.commissionStatus);
  }
  
  if (filterForm.value.orderId) {
    filtered = filtered.filter(order => 
      order.order_id.toLowerCase().includes(filterForm.value.orderId.toLowerCase())
    );
  }
  
  return filtered;
});

// 计算属性：统计数据
const stats = computed(() => {
  const filtered = filteredOrders.value;
  const serviceCount = {
    implementation: 0,
    afterSales: 0,
    sps: 0
  };
  
  filtered.forEach(order => {
    if (order.serviceItems) {
      order.serviceItems.forEach(item => {
        if (item.service_name === '实施服务') serviceCount.implementation++;
        else if (item.service_name === '售后服务') serviceCount.afterSales++;
        else if (item.service_name === 'sps服务') serviceCount.sps++;
      });
    }
  });
  
  return {
    totalOrders: filtered.length,
    totalAmount: filtered.reduce((sum, order) => sum + parseFloat(order.actual_amount || 0), 0).toFixed(2),
    serviceCount,
    commissionAmount: filtered.reduce((sum, order) => sum + parseFloat(order.commission_amount || 0), 0).toFixed(2)
  };
});

// 获取服务类型标签颜色
const getServiceTagType = (serviceName) => {
  switch (serviceName) {
    case '实施服务':
      return 'primary';
    case '售后服务':
      return 'success';
    case 'sps服务':
      return 'warning';
    default:
      return 'info';
  }
};

// 获取服务订单列表
const fetchOrders = async () => {
  loading.value = true;
  try {
    const response = await getServiceOrders();
    orders.value = response || [];
  } catch (error) {
    console.error('获取服务订单列表失败:', error);
    ElMessage.error('获取订单列表失败');
  } finally {
    loading.value = false;
  }
};

// 表格合计行
const getSummaries = (param) => {
  const { columns, data } = param;
  const sums = [];
  columns.forEach((column, index) => {
    if (index === 0) {
      sums[index] = '合计';
      return;
    }
    
    if (column.property === 'actual_amount') {
      const values = data.map(item => Number(item.actual_amount));
      if (!values.every(value => isNaN(value))) {
        sums[index] = `¥${values.reduce((prev, curr) => {
          const value = Number(curr);
          if (!isNaN(value)) {
            return prev + curr;
          } else {
            return prev;
          }
        }, 0).toFixed(2)}`;
      } else {
        sums[index] = '';
      }
    } else if (column.property === 'commission_amount') {
      const values = data.map(item => Number(item.commission_amount || 0));
      sums[index] = `¥${values.reduce((prev, curr) => prev + curr, 0).toFixed(2)}`;
    } else {
      sums[index] = '';
    }
  });
  
  return sums;
};

// 事件处理函数
const handleCreate = () => {
  router.push({ name: 'ServiceOrderCreate' });
};

const handleRefresh = () => {
  fetchOrders();
};

const handleFilter = () => {
  // 筛选逻辑已通过计算属性实现
};

const handleResetFilter = () => {
  filterForm.value = {
    orderType: '',
    creationMethod: '',
    serviceType: '',
    commissionStatus: '',
    orderId: ''
  };
};

const handleView = (order) => {
  router.push({
    name: 'ServiceOrderForm',
    params: { id: order.id },
    query: { mode: 'view' }
  });
};

// 弃审订单
const handleRevertAudit = async (order) => {
  auditingOrderId.value = order.id;
  try {
    await revertAudit(order.id, async () => {
      // 弃审成功后刷新列表
      await fetchOrders();
    });
  } finally {
    auditingOrderId.value = null;
  }
};

// 组件挂载时获取数据
onMounted(() => {
  fetchOrders();
});
</script>

<style scoped>
.service-order-list {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #303133;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.filter-bar {
  background: #f5f7fa;
  padding: 16px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.stats-cards {
  margin-bottom: 20px;
}

.stats-card {
  text-align: center;
}

.stats-content {
  padding: 10px 0;
}

.stats-value {
  font-size: 24px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 8px;
}

.stats-label {
  font-size: 14px;
  color: #909399;
}

.table-container {
  background: white;
  border-radius: 4px;
  overflow: hidden;
}

.enterprise-name {
  font-weight: 500;
  color: #303133;
}

.enterprise-id {
  font-size: 12px;
  color: #909399;
}

.service-item {
  margin-bottom: 8px;
  padding: 8px;
  background: #f5f7fa;
  border-radius: 4px;
}

.service-item:last-child {
  margin-bottom: 0;
}

.service-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.service-price {
  font-weight: 500;
  color: #67c23a;
}

.related-order, .service-remark {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.actual-amount {
  font-weight: 500;
  color: #67c23a;
}

.commission-info, .commission-amount {
  font-size: 12px;
  color: #909399;
}

.text-muted {
  color: #909399;
}
</style>
