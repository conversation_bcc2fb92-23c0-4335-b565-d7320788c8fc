<template>
  <div class="service-info">
    <!-- 服务明细表格 -->
    <div class="service-items-section">
      <div class="section-header">
        <h4>服务明细</h4>
        <el-button 
          v-if="isEditing" 
          type="primary" 
          size="small" 
          @click="addServiceItem"
        >
          <el-icon><Plus /></el-icon>
          添加服务
        </el-button>
      </div>

      <el-table 
        :data="formData.service_items" 
        border
        style="width: 100%"
        :show-header="true"
      >
        <el-table-column type="index" label="序号" width="60" align="center" />
        
        <!-- 服务名称 -->
        <el-table-column label="服务名称" width="150">
          <template #default="{ row, $index }">
            <el-select 
              v-if="isEditing"
              v-model="row.service_name" 
              placeholder="选择服务"
              style="width: 100%"
              @change="handleServiceNameChange(row, $index)"
            >
              <el-option label="实施服务" value="实施服务" />
              <el-option label="售后服务" value="售后服务" />
              <el-option label="sps服务" value="sps服务" />
            </el-select>
            <span v-else>{{ row.service_name }}</span>
          </template>
        </el-table-column>

        <!-- 标准价格 -->
        <el-table-column label="标准价格" width="120">
          <template #default="{ row }">
            <el-input
              v-if="isEditing"
              v-model.number="row.standard_price"
              type="number"
              step="0.01"
              min="0"
              placeholder="0.00"
              style="width: 100%"
              size="small"
              @input="debouncedUpdateParent"
            />
            <span v-else>¥{{ row.standard_price }}</span>
          </template>
        </el-table-column>

        <!-- 实际价格 -->
        <el-table-column label="实际价格" width="120">
          <template #default="{ row }">
            <el-input
              v-if="isEditing"
              v-model.number="row.actual_price"
              type="number"
              step="0.01"
              min="0"
              placeholder="0.00"
              style="width: 100%"
              size="small"
              @input="debouncedUpdateParent"
            />
            <span v-else class="actual-price">¥{{ row.actual_price }}</span>
          </template>
        </el-table-column>

        <!-- 关联产品订单号 -->
        <el-table-column label="关联产品订单号" width="180">
          <template #default="{ row }">
            <el-input
              v-if="isEditing"
              v-model="row.related_order_id"
              placeholder="输入产品订单号"
              size="small"
              @input="debouncedUpdateParent"
            />
            <span v-else>{{ row.related_order_id || 'N/A' }}</span>
          </template>
        </el-table-column>

        <!-- 备注 -->
        <el-table-column label="备注" min-width="200">
          <template #default="{ row }">
            <el-input 
              v-if="isEditing"
              v-model="row.remark" 
              placeholder="输入备注"
              size="small"
            />
            <span v-else>{{ row.remark || '' }}</span>
          </template>
        </el-table-column>

        <!-- 操作列 -->
        <el-table-column v-if="isEditing" label="操作" width="80" fixed="right">
          <template #default="{ $index }">
            <el-button 
              type="danger" 
              size="small" 
              @click="removeServiceItem($index)"
              :disabled="formData.service_items.length <= 1"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 空状态 -->
      <div v-if="formData.service_items.length === 0" class="empty-state">
        <el-empty description="暂无服务明细">
          <el-button v-if="isEditing" type="primary" @click="addServiceItem">
            添加第一个服务
          </el-button>
        </el-empty>
      </div>
    </div>

    <!-- 服务明细汇总 -->
    <div class="service-summary">
      <el-row :gutter="20">
        <el-col :span="8">
          <div class="summary-item">
            <span class="summary-label">服务项目数：</span>
            <span class="summary-value">{{ formData.service_items.length }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="summary-item">
            <span class="summary-label">标准价格合计：</span>
            <span class="summary-value">¥{{ totalStandardPrice }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="summary-item">
            <span class="summary-label">实际价格合计：</span>
            <span class="summary-value actual-price">¥{{ totalActualPrice }}</span>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 服务说明 -->
    <div class="service-description">
      <el-alert
        title="服务说明"
        type="info"
        :closable="false"
        show-icon
      >
        <template #default>
          <ul>
            <li><strong>实施服务：</strong>产品实施、部署、培训等服务</li>
            <li><strong>售后服务：</strong>产品使用过程中的技术支持和维护服务</li>
            <li><strong>SPS服务：</strong>软件产品支持服务，包括升级、补丁等</li>
          </ul>
        </template>
      </el-alert>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { Plus } from '@element-plus/icons-vue';

// Props
const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({})
  },
  isEditing: {
    type: Boolean,
    default: false
  }
});

// Emits
const emit = defineEmits(['update:modelValue', 'validate']);

// 响应式数据
const formData = ref({
  service_items: []
});

// 计算属性：标准价格合计
const totalStandardPrice = computed(() => {
  return formData.value.service_items
    .reduce((sum, item) => sum + (parseFloat(item.standard_price) || 0), 0)
    .toFixed(2);
});

// 计算属性：实际价格合计
const totalActualPrice = computed(() => {
  return formData.value.service_items
    .reduce((sum, item) => sum + (parseFloat(item.actual_price) || 0), 0)
    .toFixed(2);
});

// 监听props变化
watch(() => props.modelValue, (newValue) => {
  console.log('ServiceInfo组件接收到的数据:', newValue);
  if (newValue && newValue.service_items) {
    console.log('服务明细数据:', newValue.service_items);
    // 只在数据真正不同时才更新，避免循环
    const currentItems = JSON.stringify(formData.value.service_items);
    const newItems = JSON.stringify(newValue.service_items);
    if (currentItems !== newItems) {
      formData.value = {
        service_items: newValue.service_items || []
      };
    }
  } else {
    console.log('没有接收到service_items数据');
  }
}, { immediate: true });

// 手动更新父组件，而不是自动监听
const updateParent = () => {
  emit('update:modelValue', { ...formData.value });
};

// 防抖更新函数，减少频繁的父组件更新
let updateTimer = null;
const debouncedUpdateParent = () => {
  if (updateTimer) {
    clearTimeout(updateTimer);
  }
  updateTimer = setTimeout(() => {
    updateParent();
  }, 300); // 300ms 防抖延迟
};

// 创建新的服务明细项
const createServiceItem = () => {
  return {
    service_name: '',
    standard_price: 0,
    actual_price: 0,
    asset_price_field: null,
    related_order_id: '',
    remark: ''
  };
};

// 添加服务明细
const addServiceItem = () => {
  formData.value.service_items.push(createServiceItem());
  updateParent();
};

// 删除服务明细
const removeServiceItem = (index) => {
  if (formData.value.service_items.length > 1) {
    formData.value.service_items.splice(index, 1);
    updateParent();
  } else {
    ElMessage.warning('至少需要保留一个服务明细');
  }
};

// 处理服务名称变化
const handleServiceNameChange = (row, index) => {
  // 根据服务名称设置对应的资产价格字段
  const serviceFieldMapping = {
    '实施服务': 'implementation_fee',
    '售后服务': 'after_sales_service_fee',
    'sps服务': 'sps_annual_fee'
  };

  row.asset_price_field = serviceFieldMapping[row.service_name] || null;

  // 可以在这里根据服务类型设置默认价格
  if (row.service_name && row.standard_price === 0) {
    const defaultPrices = {
      '实施服务': 5000,
      '售后服务': 2000,
      'sps服务': 1000
    };

    row.standard_price = defaultPrices[row.service_name] || 0;
    row.actual_price = row.standard_price;
  }

  // 通知父组件更新
  updateParent();
};

// 表单验证
const validate = () => {
  const errors = [];
  
  if (formData.value.service_items.length === 0) {
    errors.push('至少需要添加一个服务明细');
  }
  
  formData.value.service_items.forEach((item, index) => {
    if (!item.service_name) {
      errors.push(`第${index + 1}行：请选择服务名称`);
    }
    if (!item.standard_price || item.standard_price <= 0) {
      errors.push(`第${index + 1}行：请输入有效的标准价格`);
    }
    if (!item.actual_price || item.actual_price <= 0) {
      errors.push(`第${index + 1}行：请输入有效的实际价格`);
    }
  });
  
  if (errors.length > 0) {
    ElMessage.error(errors[0]);
    emit('validate', false);
    return false;
  }
  
  emit('validate', true);
  return true;
};

// 暴露验证方法给父组件
defineExpose({
  validate
});

// 初始化：如果没有服务明细，添加一个空的
if (formData.value.service_items.length === 0 && props.isEditing) {
  addServiceItem();
}
</script>

<style scoped>
.service-info {
  background: white;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.service-items-section {
  margin-bottom: 20px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.section-header h4 {
  margin: 0;
  color: #303133;
  font-size: 16px;
}

.empty-state {
  padding: 40px 0;
  text-align: center;
}

.service-summary {
  background: #f5f7fa;
  padding: 16px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
}

.summary-label {
  color: #606266;
  font-weight: 500;
}

.summary-value {
  color: #303133;
  font-weight: 600;
}

.actual-price {
  color: #67c23a;
}

.service-description {
  margin-top: 20px;
}

.service-description ul {
  margin: 0;
  padding-left: 20px;
}

.service-description li {
  margin-bottom: 8px;
  color: #606266;
}

.service-description li:last-child {
  margin-bottom: 0;
}

/* 表格内的输入框样式调整 */
.service-info :deep(.el-table .el-input-number) {
  width: 100%;
}

.service-info :deep(.el-table .el-input) {
  width: 100%;
}

.service-info :deep(.el-table .el-select) {
  width: 100%;
}
</style>
