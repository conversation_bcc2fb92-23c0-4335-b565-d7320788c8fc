import axios from 'axios';
import { ElMessage } from 'element-plus';
import { useAuth } from '@/store/auth';

// 创建一个新的 axios 实例
const service = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || 'https://service.bogoo.net/api', // API 的 base_url
  timeout: 5000 // 请求超时时间
});

// 请求拦截器
service.interceptors.request.use(
  config => {
    // 在发送请求之前做些什么，例如添加token
    const token = localStorage.getItem('authToken'); // 从localStorage获取token
    if (token) {
      // 如果token存在，则将其添加到请求头中
      config.headers['Authorization'] = 'Bearer ' + token;
    }
    return config;
  },
  error => {
    // 对请求错误做些什么
    console.log(error); // for debug
    return Promise.reject(error);
  }
);

// 响应拦截器
service.interceptors.response.use(
  response => {
    // 对响应数据做点什么
    const res = response.data;
    // 如果是blob二进制文件，则直接返回
    if (response.request.responseType === 'blob') {
      return response;
    }
    return res;
  },
  error => {
    // 对响应错误做点什么
    console.error('API Error: ', error); // for debug

    if (error.response && error.response.status === 401) {
      const { logout } = useAuth();
      // 对于登录接口本身的401错误，只提示密码错误，不直接登出
      if (error.config.url.endsWith('/auth/employee/login')) {
        ElMessage({
          message: error.response?.data?.message || '认证失败，请检查凭据',
          type: 'error',
          duration: 5 * 1000
        });
      } else {
        // 其他所有接口的401错误，意味着token失效或无权限
        ElMessage({
          message: '会话已过期或无权访问，请重新登录。',
          type: 'error',
          duration: 5 * 1000
        });
        logout(); // 调用登出，清空状态并跳转到登录页
      }
    } else {
      // 其他非401错误，显示通用错误信息
      ElMessage({
        message: error.response?.data?.message || error.message || 'Error',
        type: 'error',
        duration: 5 * 1000
      });
    }
    
    return Promise.reject(error);
  }
);

export default service; 