const { OrderHead, OrderProductItem, OrderServiceItem, OrderAttachment, User, Enterprise, Asset, Product, Employee, sequelize } = require('../models');
const { Op } = require('sequelize');
const { generateOrderId } = require('../utils/id_helper');

/**
 * 获取订单审核列表 - 显示所有待审核的订单
 */
const getOrdersForReview = async () => {
    return await OrderHead.findAll({
        where: {
            audit_status: '待审核'
        },
        include: [
            { model: User, as: 'user', attributes: ['name', 'mobile'] },
            { model: Enterprise, as: 'enterprise', attributes: ['name'] },
            { model: Asset, as: 'asset' },
            { model: Employee, as: 'creator', attributes: ['name'] }
        ],
        order: [['created_at', 'DESC']]
    });
};

/**
 * 获取产品订单列表 - 显示已审核通过的产品订单
 */
const getProductOrders = async () => {
    return await OrderHead.findAll({
        where: {
            order_category: '产品订单',
            audit_status: '已审核'
        },
        include: [
            { model: User, as: 'user', attributes: ['name', 'mobile'] },
            { model: Enterprise, as: 'enterprise', attributes: ['name'] },
            { model: Asset, as: 'asset' },
            { model: OrderProductItem, as: 'productItems', include: [{ model: Product, as: 'product' }] },
            { model: Employee, as: 'creator', attributes: ['name'] }
        ],
        order: [['created_at', 'DESC']]
    });
};

/**
 * 获取服务订单列表 - 显示已审核通过的服务订单
 */
const getServiceOrders = async () => {
    return await OrderHead.findAll({
        where: {
            order_category: '服务订单',
            audit_status: '已审核'
        },
        include: [
            { model: User, as: 'user', attributes: ['name', 'mobile'] },
            { model: Enterprise, as: 'enterprise', attributes: ['name'] },
            { model: Asset, as: 'asset' },
            { model: OrderServiceItem, as: 'serviceItems' },
            { model: Employee, as: 'creator', attributes: ['name'] }
        ],
        order: [['created_at', 'DESC']]
    });
};

/**
 * 根据主键ID获取单个订单详情
 * @param {number} id - 订单主键ID
 * @param {object} transaction - 可选的事务对象
 */
const getOrderById = async (id, transaction = null) => {
    const options = {
        include: [
           { model: User, as: 'user' },
           { model: Enterprise, as: 'enterprise' },
           { model: Asset, as: 'asset', include: [{ model: Product, as: 'product' }] },
           { model: OrderProductItem, as: 'productItems', include: [{ model: Product, as: 'product' }] },
           { model: OrderServiceItem, as: 'serviceItems' },
           { model: OrderAttachment, as: 'attachments', include: [{ model: Employee, as: 'uploader', attributes: ['name'] }] },
           { model: Employee, as: 'creator' }
       ]
   };

   if (transaction) {
       options.transaction = transaction;
   }

   const order = await OrderHead.findByPk(id, options);
   if (!order) {
       throw new Error('订单未找到');
   }



   return order;
};

/**
 * 计算订单佣金
 * @param {string} partnerUserId - 合伙人用户ID
 * @param {number} actualAmount - 订单实付金额
 * @returns {object} 包含佣金信息的对象
 */
const calculateCommission = async (partnerUserId, actualAmount) => {
    if (!partnerUserId || !actualAmount) {
        return {
            commission_base: null,
            commission_extra: null,
            commission_amount: 0
        };
    }

    // 根据合伙人用户ID查找用户信息
    const partner = await User.findOne({
        where: { id: partnerUserId }
    });

    if (!partner || !partner.is_partner) {
        return {
            commission_base: null,
            commission_extra: null,
            commission_amount: 0
        };
    }

    const commissionBase = partner.commission_base || 0;
    const commissionExtra = partner.commission_extra || 0;
    const totalCommissionRate = commissionBase + commissionExtra;
    const commissionAmount = actualAmount * totalCommissionRate;

    return {
        commission_base: commissionBase,
        commission_extra: commissionExtra,
        commission_amount: commissionAmount
    };
};

/**
 * 创建产品订单
 * @param {object} orderData - 包含订单信息的对象
 * @param {number} creatorId - 创建人ID
 */
const createProductOrder = async (orderData, creatorId) => {
    return sequelize.transaction(async (t) => {
        // 清理可选外键，防止空字符串导致约束失败
        const cleanOrderData = { ...orderData };
        cleanOrderData.asset_id = (cleanOrderData.asset_id === '' || cleanOrderData.asset_id === undefined) ? null : cleanOrderData.asset_id;
        cleanOrderData.enterprise_id = (cleanOrderData.enterprise_id === '' || cleanOrderData.enterprise_id === undefined) ? null : cleanOrderData.enterprise_id;
        cleanOrderData.partner_user_id = (cleanOrderData.partner_user_id === '' || cleanOrderData.partner_user_id === undefined) ? null : cleanOrderData.partner_user_id;
        cleanOrderData.user_id = (cleanOrderData.user_id === '' || cleanOrderData.user_id === undefined) ? null : cleanOrderData.user_id;

        // 添加调试日志
        console.log('产品订单数据清理后:', {
            user_id: cleanOrderData.user_id,
            enterprise_id: cleanOrderData.enterprise_id,
            user_id_type: typeof cleanOrderData.user_id,
            enterprise_id_type: typeof cleanOrderData.enterprise_id
        });

        // 业务规则校验
        if (!cleanOrderData.user_id && !cleanOrderData.enterprise_id) {
            throw new Error('创建订单失败：必须关联一个用户或一个企业。');
        }

        // 设置订单类型
        cleanOrderData.order_category = '产品订单';
        cleanOrderData.creator_id = creatorId;

        // 自动生成或验证 order_id
        if (!cleanOrderData.order_id) {
            cleanOrderData.order_id = await generateOrderId('PO');
        } else {
            const existing = await OrderHead.findOne({ where: { order_id: cleanOrderData.order_id }, transaction: t });
            if (existing) {
                throw new Error(`订单ID '${cleanOrderData.order_id}' 已存在。`);
            }
        }

        // 如果是合伙人订单，计算佣金
        if (cleanOrderData.partner_user_id && cleanOrderData.actual_amount) {
            const commissionInfo = await calculateCommission(cleanOrderData.partner_user_id, cleanOrderData.actual_amount);
            cleanOrderData.commission_base = commissionInfo.commission_base;
            cleanOrderData.commission_extra = commissionInfo.commission_extra;
            cleanOrderData.commission_amount = commissionInfo.commission_amount;
            cleanOrderData.is_partner_order = 1;
            cleanOrderData.commission_status = '未发放';
        }

        // 创建订单表头
        const newOrder = await OrderHead.create(cleanOrderData, { transaction: t });

        // 创建产品订单表体
        if (orderData.productItem) {
            const productItemData = {
                ...orderData.productItem,
                order_id: newOrder.id
            };

            // 调试信息：检查product_id是否存在
            console.log('产品订单表体数据:', {
                product_id: productItemData.product_id,
                order_id: productItemData.order_id,
                user_count: productItemData.user_count,
                account_count: productItemData.account_count
            });

            // 验证必要字段
            if (!productItemData.product_id) {
                throw new Error('产品ID不能为空');
            }

            await OrderProductItem.create(productItemData, { transaction: t });
        } else {
            console.log('警告：没有产品订单表体数据');
        }

        // 返回新创建的、完整的订单信息
        return await getOrderById(newOrder.id, t);
    });
};

/**
 * 创建服务订单
 * @param {object} orderData - 包含订单信息的对象
 * @param {number} creatorId - 创建人ID
 */
const createServiceOrder = async (orderData, creatorId) => {
    return sequelize.transaction(async (t) => {
        // 清理可选外键，防止空字符串导致约束失败
        const cleanOrderData = { ...orderData };
        cleanOrderData.asset_id = (cleanOrderData.asset_id === '' || cleanOrderData.asset_id === undefined) ? null : cleanOrderData.asset_id;
        cleanOrderData.enterprise_id = (cleanOrderData.enterprise_id === '' || cleanOrderData.enterprise_id === undefined) ? null : cleanOrderData.enterprise_id;
        cleanOrderData.partner_user_id = (cleanOrderData.partner_user_id === '' || cleanOrderData.partner_user_id === undefined) ? null : cleanOrderData.partner_user_id;
        cleanOrderData.user_id = (cleanOrderData.user_id === '' || cleanOrderData.user_id === undefined) ? null : cleanOrderData.user_id;

        // 添加调试日志
        console.log('服务订单数据清理后:', {
            user_id: cleanOrderData.user_id,
            enterprise_id: cleanOrderData.enterprise_id,
            user_id_type: typeof cleanOrderData.user_id,
            enterprise_id_type: typeof cleanOrderData.enterprise_id
        });

        // 业务规则校验
        if (!cleanOrderData.user_id && !cleanOrderData.enterprise_id) {
            throw new Error('创建订单失败：必须关联一个用户或一个企业。');
        }

        // 设置订单类型
        cleanOrderData.order_category = '服务订单';
        cleanOrderData.creator_id = creatorId;

        // 自动生成或验证 order_id
        if (!cleanOrderData.order_id) {
            cleanOrderData.order_id = await generateOrderId('SO');
        } else {
            const existing = await OrderHead.findOne({ where: { order_id: cleanOrderData.order_id }, transaction: t });
            if (existing) {
                throw new Error(`订单ID '${cleanOrderData.order_id}' 已存在。`);
            }
        }

        // 如果是合伙人订单，计算佣金
        if (cleanOrderData.partner_user_id && cleanOrderData.actual_amount) {
            const commissionInfo = await calculateCommission(cleanOrderData.partner_user_id, cleanOrderData.actual_amount);
            cleanOrderData.commission_base = commissionInfo.commission_base;
            cleanOrderData.commission_extra = commissionInfo.commission_extra;
            cleanOrderData.commission_amount = commissionInfo.commission_amount;
            cleanOrderData.is_partner_order = 1;
            cleanOrderData.commission_status = '未发放';
        }

        // 创建订单表头
        const newOrder = await OrderHead.create(cleanOrderData, { transaction: t });

        // 创建服务订单表体
        if (orderData.serviceItems && orderData.serviceItems.length > 0) {
            const serviceItemsData = orderData.serviceItems.map(item => ({
                ...item,
                order_id: newOrder.id
            }));
            await OrderServiceItem.bulkCreate(serviceItemsData, { transaction: t });
        }

        // 返回新创建的、完整的订单信息
        return await getOrderById(newOrder.id, t);
    });
};



/**
 * 更新订单
 * @param {number} id - 订单主键ID
 * @param {object} orderData - 要更新的订单数据
 */
const updateOrder = async (id, orderData) => {
    return sequelize.transaction(async (t) => {
        const orderToUpdate = await OrderHead.findByPk(id, { transaction: t });
        if (!orderToUpdate) {
            throw new Error('订单未找到');
        }

        // 清理可选外键
        const cleanOrderData = { ...orderData };
        cleanOrderData.asset_id = cleanOrderData.asset_id || null;
        cleanOrderData.enterprise_id = cleanOrderData.enterprise_id || null;
        cleanOrderData.partner_user_id = cleanOrderData.partner_user_id || null;
        cleanOrderData.user_id = cleanOrderData.user_id || null;

        await orderToUpdate.update(cleanOrderData, { transaction: t });

        // 返回更新后的完整订单数据
        return await getOrderById(id);
    });
};

/**
 * 审核订单 - 通过审核
 * @param {number} id - 订单主键ID
 */
const approveOrder = async (id) => {
    return sequelize.transaction(async (t) => {
        const order = await OrderHead.findByPk(id, { transaction: t });
        if (!order) {
            throw new Error('订单未找到');
        }

        await order.update({ audit_status: '已审核' }, { transaction: t });
        return await getOrderById(id);
    });
};

/**
 * 审核订单 - 拒绝审核
 * @param {number} id - 订单主键ID
 * @param {string} reason - 拒绝原因
 */
const rejectOrder = async (id, reason) => {
    return sequelize.transaction(async (t) => {
        const order = await OrderHead.findByPk(id, { transaction: t });
        if (!order) {
            throw new Error('订单未找到');
        }

        const updateData = {
            audit_status: '已拒绝'
        };
        if (reason) {
            updateData.remark = reason;
        }

        await order.update(updateData, { transaction: t });
        return await getOrderById(id);
    });
};

/**
 * 删除订单
 * @param {number} id - 订单主键ID
 */
const deleteOrder = async (id) => {
    return sequelize.transaction(async (t) => {
        // 先检查订单是否存在
        const order = await OrderHead.findByPk(id, { transaction: t });
        if (!order) {
            throw new Error("订单未找到");
        }

        // 删除关联的订单明细
        await OrderProductItem.destroy({
            where: { order_id: id },
            transaction: t
        });

        await OrderServiceItem.destroy({
            where: { order_id: id },
            transaction: t
        });

        // 删除关联的附件
        await OrderAttachment.destroy({
            where: { order_id: id },
            transaction: t
        });

        // 最后删除订单头
        const deleted = await OrderHead.destroy({
            where: { id: id },
            transaction: t
        });

        if (!deleted) {
            throw new Error("订单删除失败");
        }

        return { message: "订单删除成功" };
    });
};

/**
 * 获取合伙人订单列表
 * @param {string} userId - 用户ID
 * @param {object} options - 查询选项
 */
const getPartnerOrders = async (userId, options = {}) => {
    const { page = 1, pageSize = 100 } = options;

    // 验证用户是否为合伙人
    const user = await User.findByPk(userId);
    if (!user || !user.is_partner) {
        throw new Error('您不是合伙人，无法查看订单信息');
    }

    // 查询合伙人相关的订单
    const offset = (page - 1) * pageSize;
    const limit = parseInt(pageSize);

    const { count, rows: orders } = await OrderHead.findAndCountAll({
        where: {
            partner_user_id: user.id,  // 使用user.id而不是user.user_id
            is_partner_order: 1
        },
        include: [
            {
                model: Enterprise,
                as: 'enterprise',
                attributes: ['id', 'name']
            },
            {
                model: Asset,
                as: 'asset',
                include: [
                    {
                        model: Product,
                        as: 'product',
                        attributes: ['id', 'product_name']
                    }
                ]
            }
        ],
        order: [['created_at', 'DESC']],
        offset,
        limit
    });

    // 格式化订单数据
    const formattedOrders = orders.map(order => ({
        order_id: order.order_id,
        product_name: order.asset?.product?.product_name || '未知产品',
        company_name: order.enterprise?.name || '未知公司',
        actual_amount: order.actual_amount,
        commission_amount: order.commission_amount,
        commission_status: order.commission_status,
        payment_status: order.payment_status,
        created_at: order.created_at,
        ...order.toJSON()
    }));

    return {
        orders: formattedOrders,
        total: count,
        page: parseInt(page),
        pageSize: limit
    };
};


module.exports = {
    getOrdersForReview,
    getProductOrders,
    getServiceOrders,
    getOrderById,
    createProductOrder,
    createServiceOrder,
    updateOrder,
    approveOrder,
    rejectOrder,
    deleteOrder,
    getPartnerOrders
};