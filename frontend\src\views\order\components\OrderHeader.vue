<template>
  <div class="order-header">
    <el-form :model="formData" :rules="rules" ref="formRef" label-width="120px">
      <el-row :gutter="20">
        <!-- 第一行：订单基本信息 -->
        <el-col :span="8">
          <el-form-item label="订单号" prop="order_id">
            <el-input
              v-model="formData.order_id"
              :disabled="!isEditing"
              placeholder="自动生成，可手工修改"
              @blur="validateOrderId"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="订单大类" prop="order_category">
            <el-select
              v-model="formData.order_category"
              disabled
              style="width: 100%"
            >
              <el-option label="产品订单" value="产品订单" />
              <el-option label="服务订单" value="服务订单" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="创建方式" prop="creation_method">
            <el-select 
              v-model="formData.creation_method" 
              :disabled="!isEditing"
              style="width: 100%"
            >
              <el-option label="手工创建" value="手工创建" />
              <el-option label="用户创建" value="用户创建" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <!-- 第二行：关联信息 -->
        <el-col :span="8">
          <el-form-item label="企业" prop="enterprise_id">
            <el-select
              v-model="formData.enterprise_id"
              :disabled="!isEditing"
              style="width: 100%"
              filterable
              remote
              :remote-method="searchEnterprises"
              :loading="enterpriseLoading"
              placeholder="选择企业"
              @change="handleEnterpriseChange"
              @focus="initializeEntityData"
              clearable
            >
              <el-option
                v-for="enterprise in enterprises"
                :key="enterprise.id"
                :label="`${enterprise.name} (${enterprise.enterprise_id})`"
                :value="enterprise.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="资产" prop="asset_id">
            <el-select 
              v-model="formData.asset_id" 
              :disabled="!isEditing"
              style="width: 100%"
              filterable
              placeholder="选择资产"
              @change="handleAssetChange"
              clearable
            >
              <el-option
                v-for="asset in availableAssets"
                :key="asset.id"
                :label="`${asset.asset_id} - ${asset.product?.product_name || 'N/A'} - ${asset.product?.version_name || 'N/A'}`"
                :value="asset.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="用户" prop="user_id">
            <el-select
              v-model="formData.user_id"
              :disabled="!isEditing"
              style="width: 100%"
              filterable
              remote
              :remote-method="searchUsers"
              :loading="userLoading"
              placeholder="选择用户"
              @focus="initializeEntityData"
              @change="handleUserChange"
              clearable
            >
              <el-option
                v-for="user in users"
                :key="user.id"
                :label="`${user.name} (${user.mobile})`"
                :value="user.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <!-- 第三行：订单类型和金额 -->
        <el-col :span="8">
          <el-form-item label="订单类型" prop="order_type">
            <el-select 
              v-model="formData.order_type" 
              :disabled="!isEditing"
              style="width: 100%"
            >
              <el-option label="普通订单" value="普通订单" />
              <el-option label="续费订单" value="续费订单" />
              <el-option label="变更订单" value="变更订单" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="标准价格" prop="standard_amount">
            <el-input
              v-model.number="formData.standard_amount"
              :disabled="!isEditing"
              type="number"
              step="0.01"
              min="0"
              placeholder="0.00"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="实付价格" prop="actual_amount">
            <el-input
              v-model.number="formData.actual_amount"
              :disabled="!isEditing"
              type="number"
              step="0.01"
              min="0"
              placeholder="0.00"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 变更详情字段（仅变更订单显示） -->
      <el-row :gutter="20" v-if="formData.order_type === '变更订单'">
        <el-col :span="24">
          <el-form-item label="变更详情" prop="change_details">
            <el-input
              v-model="formData.change_details"
              :disabled="!isEditing"
              type="textarea"
              :rows="3"
              placeholder="请详细描述本次变更的具体内容..."
              style="width: 100%"
              class="change-details-input"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 产品订单特有字段 -->
      <div v-if="formData.order_category === '产品订单'">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="税额">
              <el-input
                v-model.number="formData.tax_amount"
                :disabled="!isEditing"
                type="number"
                step="0.01"
                min="0"
                placeholder="0.00"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="发票类型">
              <el-select
                v-model="formData.invoice_type"
                :disabled="!isEditing"
                style="width: 100%"
              >
                <el-option label="不开票" value="不开票" />
                <el-option label="普票" value="普票" />
                <el-option label="专票" value="专票" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 支付状态字段 - 产品订单和服务订单都显示 -->
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="支付状态">
            <!-- 编辑模式：显示开关 -->
            <el-switch
              v-if="isEditing"
              v-model="isPaid"
              active-text="已支付"
              inactive-text="待支付"
              @change="handlePaymentStatusChange"
            />
            <!-- 查看模式：显示状态标签 -->
            <el-tag
              v-else
              :type="formData.payment_status === '已支付' ? 'success' : 'warning'"
              size="default"
            >
              {{ formData.payment_status }}
            </el-tag>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 只有已支付时才显示支付方式和支付时间 -->
      <el-row v-if="isPaid" :gutter="20">
        <el-col :span="8">
          <el-form-item label="支付方式">
            <el-select
              v-model="formData.payment_method"
              :disabled="!isEditing"
              style="width: 100%"
            >
              <el-option label="在线" value="在线" />
              <el-option label="对公" value="对公" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="支付时间">
            <el-date-picker
              v-model="formData.payment_time"
              :disabled="!isEditing"
              type="datetime"
              placeholder="选择支付时间"
              style="width: 100%"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              :default-time="new Date(12, 0, 0)"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 合伙人信息 -->
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="合伙人订单">
            <el-switch 
              v-model="formData.is_partner_order" 
              :disabled="!isEditing"
              @change="handlePartnerOrderChange"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8" v-if="formData.is_partner_order">
          <el-form-item label="合伙人">
            <el-select
              v-model="formData.partner_user_id"
              :disabled="!isEditing"
              style="width: 100%"
              filterable
              remote
              :remote-method="searchPartners"
              :loading="partnerLoading"
              placeholder="选择合伙人"
              clearable
              @focus="handlePartnerFocus"
              @change="handlePartnerChange"
              :key="`partner-select-${partners.length}-${formData.partner_user_id}`"
            >
              <el-option
                v-for="partner in partners"
                :key="partner.id"
                :label="`${partner.name} (ID: ${partner.partner_id || partner.id})`"
                :value="partner.id"
              >
                <span style="float: left">{{ partner.name }}</span>
                <span style="float: right; color: #8492a6; font-size: 13px">
                  ID: {{ partner.partner_id || partner.id }}
                </span>
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="4" v-if="formData.is_partner_order">
          <el-form-item label="基础分润">
            <el-input-number
              v-model="formData.commission_base"
              :disabled="!isEditing"
              :precision="4"
              :min="0"
              :max="1"
              :step="0.01"
              :controls="false"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="4" v-if="formData.is_partner_order">
          <el-form-item label="额外分润">
            <el-input-number
              v-model="formData.commission_extra"
              :disabled="!isEditing"
              :precision="4"
              :min="0"
              :max="1"
              :step="0.01"
              :controls="false"
              placeholder="如0.02表示2%"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20" v-if="formData.is_partner_order">
        <el-col :span="8">
          <el-form-item label="分润金额">
            <el-input-number
              v-model="formData.commission_amount"
              :disabled="!isEditing"
              :precision="2"
              :min="0"
              :controls="false"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="分润状态">
            <el-select 
              v-model="formData.commission_status" 
              :disabled="!isEditing"
              style="width: 100%"
            >
              <el-option label="已发放" value="已发放" />
              <el-option label="未发放" value="未发放" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>


    </el-form>
  </div>
</template>

<script setup>
import { ref, watch, onMounted, nextTick } from 'vue';
import { ElMessage } from 'element-plus';
import { getNextOrderId, checkOrderIdExists } from '@/api/order';
import { getUsers, getUserById } from '@/api/user';
import { useEntityLinkage } from '@/composables/useEntityLinkage';
import { getCurrentDateTime, toLocalDateTime } from '@/utils/format';

// Props
const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({})
  },
  isEditing: {
    type: Boolean,
    default: false
  },
  mode: {
    type: String,
    default: 'create' // create, edit, view, review
  }
});

// Emits
const emit = defineEmits(['update:modelValue', 'validate']);

// 响应式数据
const formRef = ref(null);
const formData = ref({
  order_id: '',
  order_category: '产品订单',
  enterprise_id: null,
  asset_id: null,
  user_id: null,
  creation_method: '手工创建',
  order_type: '普通订单',
  standard_amount: 0,
  actual_amount: 0,
  tax_amount: null,
  invoice_type: '不开票',
  payment_status: '已支付',
  payment_method: '在线',
  payment_time: null,
  is_partner_order: false,
  partner_user_id: null,
  commission_base: null,
  commission_extra: null,
  commission_amount: null,
  commission_status: '未发放',
  remark: '',
  change_details: ''
});

// 使用企业、用户、资产联动逻辑
const {
  enterprises,
  users,
  assets: availableAssets,
  enterpriseLoading,
  userLoading,
  assetLoading,
  selectedEnterprise,
  selectedUser,
  selectedAsset,
  isValid,
  searchEnterprises,
  searchUsers,
  handleEnterpriseChange: onEnterpriseChange,
  handleUserChange: onUserChange,
  handleAssetChange: onAssetChange,
  initialize: initializeEntityData
} = useEntityLinkage();

// 支付状态开关
const isPaid = ref(false);

// 合伙人搜索相关
const partners = ref([]);
const partnerLoading = ref(false);

// 表单验证规则
const rules = {
  order_category: [
    { required: true, message: '请选择订单大类', trigger: 'change' }
  ],
  creation_method: [
    { required: true, message: '请选择创建方式', trigger: 'change' }
  ],
  order_type: [
    { required: true, message: '请选择订单类型', trigger: 'change' }
  ],
  enterprise_id: [
    {
      validator: (rule, value, callback) => {
        // 企业ID和用户ID不能同时为空
        const enterpriseId = value !== undefined ? value : formData.value.enterprise_id;
        const userId = formData.value.user_id;

        // 检查是否都为空值（null、undefined、0、''）
        const isEnterpriseEmpty = enterpriseId === null || enterpriseId === undefined || enterpriseId === '' || enterpriseId === 0;
        const isUserEmpty = userId === null || userId === undefined || userId === '' || userId === 0;

        if (isEnterpriseEmpty && isUserEmpty) {
          callback(new Error('企业和用户不能同时为空，请至少选择一个'));
        } else {
          callback();
        }
      },
      trigger: ['change', 'blur']
    }
  ],
  user_id: [
    {
      validator: (rule, value, callback) => {
        // 企业ID和用户ID不能同时为空
        const userId = value !== undefined ? value : formData.value.user_id;
        const enterpriseId = formData.value.enterprise_id;

        // 检查是否都为空值（null、undefined、0、''）
        const isEnterpriseEmpty = enterpriseId === null || enterpriseId === undefined || enterpriseId === '' || enterpriseId === 0;
        const isUserEmpty = userId === null || userId === undefined || userId === '' || userId === 0;

        if (isEnterpriseEmpty && isUserEmpty) {
          callback(new Error('企业和用户不能同时为空，请至少选择一个'));
        } else {
          callback();
        }
      },
      trigger: ['change', 'blur']
    }
  ],
  standard_amount: [
    { required: true, message: '请输入标准金额', trigger: 'blur' }
  ],
  actual_amount: [
    { required: true, message: '请输入实付金额', trigger: 'blur' }
  ]
};

// 标志变量，避免在props更新时触发循环
let isUpdatingFromProps = false;

// 简化的数据同步机制
watch(() => props.modelValue, (newValue) => {
  if (newValue && typeof newValue === 'object') {
    isUpdatingFromProps = true;



    // 合并数据，但不用null值覆盖已有的正确值
    const mergedData = {
      ...formData.value, // 保留默认值
      ...newValue,       // 覆盖传入的值
      // 确保关键字段有默认值
      creation_method: newValue.creation_method || '手工创建',
      order_type: newValue.order_type || '普通订单',
      payment_method: newValue.payment_method || '在线',
      invoice_type: newValue.invoice_type || '不开票',
      payment_status: newValue.payment_status || '已支付',
      audit_status: newValue.audit_status || '待审核'
    };

    // 特殊处理 commission_status：只有在真正变化时才更新，避免不必要的重新渲染
    if (newValue.commission_status !== undefined && newValue.commission_status !== formData.value.commission_status) {
      mergedData.commission_status = newValue.commission_status;
    } else if (!formData.value.commission_status) {
      mergedData.commission_status = '未发放';
    }

    // 处理支付时间的时区问题
    if (newValue.payment_time) {
      mergedData.payment_time = toLocalDateTime(newValue.payment_time);
    }

    // 修复：标准化布尔值字段（数据库中 0/1 转换为 true/false）
    if (newValue.is_partner_order !== undefined) {
      mergedData.is_partner_order = newValue.is_partner_order === true || newValue.is_partner_order === 1;
    }

    // 修复：标准化 partner_user_id 字段（确保类型一致）
    // 兼容旧字段名 partner_id
    const partnerUserId = newValue.partner_user_id || newValue.partner_id;
    if (partnerUserId !== undefined && partnerUserId !== null) {
      mergedData.partner_user_id = parseInt(partnerUserId);
    }

    // 处理合伙人信息：如果有合伙人ID但下拉列表中没有对应的合伙人信息，则加载
    const partnerUserIdForLoad = newValue.partner_user_id || newValue.partner_id;
    if (partnerUserIdForLoad && mergedData.is_partner_order) {
      const existingPartner = partners.value.find(p => p.id === partnerUserIdForLoad);
      if (!existingPartner) {
        // 异步加载合伙人信息，避免阻塞数据合并
        nextTick(() => {
          loadPartnerInfo(partnerUserIdForLoad);
        });
      }
    }

    // 特殊处理：如果传入的enterprise_id或user_id是null，但当前formData中有值，则保留当前值
    if (newValue.enterprise_id === null && formData.value.enterprise_id !== null) {
      mergedData.enterprise_id = formData.value.enterprise_id;

    }
    if (newValue.user_id === null && formData.value.user_id !== null) {
      mergedData.user_id = formData.value.user_id;

    }

    formData.value = mergedData;



    // 使用nextTick确保DOM更新后再重置标志
    nextTick(() => {
      isUpdatingFromProps = false;
    });
  }
}, { immediate: true });

// 监听 composable 中的选择变化，同步到表单数据
watch([selectedEnterprise, selectedUser, selectedAsset], ([enterpriseId, userId, assetId]) => {


  if (enterpriseId !== formData.value.enterprise_id) {
    formData.value.enterprise_id = enterpriseId;

  }
  if (userId !== formData.value.user_id) {
    formData.value.user_id = userId;

  }
  if (assetId !== formData.value.asset_id) {
    formData.value.asset_id = assetId;

  }
});

// 监听表单数据变化，同步到 composable
watch(() => formData.value.enterprise_id, (newVal) => {
  if (newVal !== selectedEnterprise.value) {
    selectedEnterprise.value = newVal;
  }
});

watch(() => formData.value.user_id, (newVal) => {
  if (newVal !== selectedUser.value) {
    selectedUser.value = newVal;
  }
});

watch(() => formData.value.asset_id, (newVal) => {
  if (newVal !== selectedAsset.value) {
    selectedAsset.value = newVal;
  }
});

// 监听支付状态变化，同步开关状态
watch(() => formData.value.payment_status, (newVal) => {
  isPaid.value = newVal === '已支付';
}, { immediate: true });

// 简化的更新函数
const updateParent = () => {

  emit('update:modelValue', { ...formData.value });
};

// 监听formData变化，自动同步到父组件（避免在props更新时触发）
watch(() => formData.value, () => {
  if (!isUpdatingFromProps) {
    updateParent();
  }
}, { deep: true });

// 监听合伙人ID变化，自动加载合伙人信息和分润比例
watch(() => formData.value.partner_user_id, async (newPartnerId, oldPartnerId) => {
  if (newPartnerId && newPartnerId !== oldPartnerId) {
    await loadPartnerInfo(newPartnerId);
  }
}, { immediate: true });

// 监听分润比例和实付金额变化，自动计算分润金额
watch([() => formData.value.commission_base, () => formData.value.commission_extra, () => formData.value.actual_amount], () => {
  // 使用 nextTick 确保数据更新完成后再计算
  nextTick(() => {
    calculateCommissionAmount();
  });
}, { immediate: true });

// 计算分润金额
const calculateCommissionAmount = () => {
  // 只有在合伙人订单模式下才计算分润金额
  // 修复：处理数据库中 is_partner_order 为 0/1 的情况
  if (!(formData.value.is_partner_order === true || formData.value.is_partner_order === 1)) {
    return;
  }

  const baseRatio = parseFloat(formData.value.commission_base) || 0;
  const extraRatio = parseFloat(formData.value.commission_extra) || 0;
  const totalRatio = baseRatio + extraRatio;
  const actualAmount = parseFloat(formData.value.actual_amount);

  if (totalRatio > 0 && actualAmount && actualAmount > 0) {
    // 分润金额 = (基础分润比例 + 额外分润比例) * 实付金额
    const newCommissionAmount = parseFloat((totalRatio * actualAmount).toFixed(2));

    // 只有金额真正变化时才更新，避免不必要的响应式更新
    if (formData.value.commission_amount !== newCommissionAmount) {
      formData.value.commission_amount = newCommissionAmount;


    }
  } else {
    if (formData.value.commission_amount !== null) {
      formData.value.commission_amount = null;
    }
  }
};

// 注意：在分离架构中，订单类型是固定的，不需要处理变化

// 企业、用户、资产的搜索和加载逻辑已移至 useEntityLinkage composable

// 资产加载逻辑已移至 useEntityLinkage composable

// 生成订单号的函数
const generateOrderId = async (orderType) => {
  try {
    const response = await getNextOrderId(orderType);
    formData.value.order_id = response.next_id;

  } catch (error) {
    console.error('获取订单号失败:', error);
    ElMessage.error('获取订单号失败');
  }
};

// 验证订单号是否重复
const validateOrderId = async () => {
  if (!formData.value.order_id || formData.value.order_id.trim() === '') {
    return;
  }

  try {
    const response = await checkOrderIdExists(formData.value.order_id);
    if (response.exists) {
      ElMessage.error(`订单号 ${formData.value.order_id} 已存在，请修改`);
      // 可以选择清空订单号或者让用户手动修改
    }
  } catch (error) {
    console.error('验证订单号失败:', error);
    ElMessage.error('验证订单号失败');
  }
};



// 处理企业变化
const handleEnterpriseChange = async (enterpriseId) => {
  // 确保数据类型正确（转换为数字或null）
  const cleanEnterpriseId = enterpriseId ? parseInt(enterpriseId) : null;

  // 更新表单数据
  formData.value.enterprise_id = cleanEnterpriseId;



  // 调用 composable 的处理函数
  await onEnterpriseChange(cleanEnterpriseId);

  // 同步 composable 的选择到表单数据
  if (!cleanEnterpriseId) {
    formData.value.asset_id = null;
  }

  // 触发用户字段验证
  formRef.value?.validateField('user_id');
  // 也触发企业字段验证
  formRef.value?.validateField('enterprise_id');

  // 立即同步数据到父组件
  updateParent();
};

// 处理资产变化
const handleAssetChange = async (assetId) => {
  // 更新表单数据
  formData.value.asset_id = assetId;

  // 调用 composable 的处理函数
  await onAssetChange(assetId);

  // 立即同步数据到父组件
  updateParent();
};

// 处理用户变化
const handleUserChange = async (userId) => {
  // 确保数据类型正确（转换为数字或null）
  const cleanUserId = userId ? parseInt(userId) : null;

  // 更新表单数据
  formData.value.user_id = cleanUserId;



  // 调用 composable 的处理函数
  await onUserChange(cleanUserId);

  // 触发企业字段验证
  formRef.value?.validateField('enterprise_id');
  // 也触发用户字段验证
  formRef.value?.validateField('user_id');

  // 立即同步数据到父组件
  updateParent();
};

// 联动逻辑已移至 useEntityLinkage composable

// 处理支付状态变化
const handlePaymentStatusChange = (value) => {
  formData.value.payment_status = value ? '已支付' : '待支付';

  // 如果改为未支付，清空支付方式和支付时间
  if (!value) {
    formData.value.payment_method = '在线';
    formData.value.payment_time = null;
  } else {
    // 如果改为已支付且没有支付时间，设置当前本地时间
    if (!formData.value.payment_time) {
      formData.value.payment_time = getCurrentDateTime();
    }
  }
};

// 处理合伙人订单变化
const handlePartnerOrderChange = (value) => {
  if (!value) {
    // 关闭合伙人订单时，清空相关字段
    formData.value.partner_user_id = null;
    formData.value.commission_base = null;
    formData.value.commission_extra = null;
    formData.value.commission_amount = null;
    formData.value.commission_status = '未发放';
    partners.value = []; // 清空合伙人列表
  }
};

// 处理合伙人选择器获得焦点
const handlePartnerFocus = () => {
  // 每次获得焦点时都重新加载合伙人数据，确保数据是最新的
  searchPartners('');
};

// 处理合伙人选择变化
const handlePartnerChange = async (partnerId) => {
  if (partnerId) {
    await loadPartnerInfo(partnerId);
  } else {
    // 清空合伙人相关信息
    formData.value.commission_base = null;
    formData.value.commission_extra = null;
    formData.value.commission_amount = null;
  }

  // 立即同步数据到父组件
  updateParent();
};

// 加载特定合伙人信息
const loadPartnerInfo = async (partnerId) => {
  if (!partnerId) return;

  try {


    // 先检查当前合伙人列表中是否已有该合伙人
    let partner = partners.value.find(p => p.id === partnerId);

    if (!partner) {
      // 如果没有，则通过API获取
      const response = await getUserById(partnerId);
      partner = response;


      // 将合伙人添加到列表中（如果是合伙人的话）
      if (partner && (partner.is_partner === true || partner.is_partner === 1)) {
        // 检查是否已存在，避免重复添加
        const existingPartner = partners.value.find(p => p.id === partner.id);
        if (!existingPartner) {
          partners.value.push(partner);


          // 强制更新选择器显示
          await nextTick();

          // 如果当前选中的就是这个合伙人，触发一次重新选择以更新显示
          if (formData.value.partner_user_id === partner.id) {
            const currentPartnerId = formData.value.partner_user_id;
            formData.value.partner_user_id = null;
            await nextTick();
            formData.value.partner_user_id = currentPartnerId;
          }
        }
      }
    } else {

    }

    if (partner) {
      // 自动填充分润比例（只在创建模式或分润比例为空时填充）
      if ((!formData.value.commission_base || formData.value.commission_base === 0) && partner.commission_base) {
        formData.value.commission_base = partner.commission_base;
      }
      if ((!formData.value.commission_extra || formData.value.commission_extra === 0) && partner.commission_extra) {
        formData.value.commission_extra = partner.commission_extra;
      }

      // 填充分润比例后立即计算分润金额
      if (partner.commission_base || partner.commission_extra) {
        nextTick(() => {
          calculateCommissionAmount();
          // 计算完成后同步数据到父组件
          updateParent();
        });
      } else {
        // 即使没有填充分润比例，也要尝试计算（可能实付金额已经有值）
        nextTick(() => {
          calculateCommissionAmount();
          // 计算完成后同步数据到父组件
          updateParent();
        });
      }
    }
  } catch (error) {
    console.error('加载合伙人信息失败:', error);
  }
};

// 搜索合伙人
const searchPartners = async (query) => {
  partnerLoading.value = true;
  try {
    // 调用用户API搜索合伙人（is_partner=true的用户）
    const response = await getUsers({ q: query || '' });

    // 后端直接返回用户数组，不是包装在data字段中
    const allUsers = response || [];

    // 过滤出合伙人用户 (处理数字类型的布尔值：1表示true，0表示false)
    partners.value = allUsers.filter(user => user.is_partner === true || user.is_partner === 1);
  } catch (error) {
    console.error('搜索合伙人失败:', error);
    partners.value = [];
  } finally {
    partnerLoading.value = false;
  }
};

// 表单验证
const validate = async () => {
  try {


    await formRef.value.validate();
    emit('validate', true);
    return true;
  } catch (error) {
    console.error('OrderHeader 验证失败:', error);
    emit('validate', false);
    return false;
  }
};

// 暴露验证方法给父组件
defineExpose({
  validate
});

// 组件挂载时初始化
onMounted(async () => {


  // 如果是创建模式且正在编辑，自动生成订单号
  if (props.mode === 'create' && props.isEditing && (!formData.value.order_id || formData.value.order_id === '')) {
    const orderType = formData.value.order_category === '产品订单' ? 'product' : 'service';

    await generateOrderId(orderType);
  } else {

  }

  // 初始化企业、用户、资产数据
  await initializeEntityData();

  // 如果已有合伙人ID，加载合伙人信息
  if (formData.value.partner_user_id) {
    await loadPartnerInfo(formData.value.partner_user_id);
  }
});
</script>

<style scoped>
.order-header {
  background: white;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.order-header .el-form-item {
  margin-bottom: 18px;
}

.order-header .el-input-number {
  width: 100%;
}

/* 变更详情字段样式 - 突出显示 */
.change-details-input {
  border: 2px solid #f56c6c !important;
  border-radius: 6px;
}

.change-details-input .el-textarea__inner {
  border: none !important;
  background-color: #fef0f0;
  font-weight: 500;
  color: #606266;
}

.change-details-input .el-textarea__inner:focus {
  background-color: #fff;
  border: 2px solid #409eff !important;
}

/* 变更详情标签样式 */
.order-header .el-form-item:has(.change-details-input) .el-form-item__label {
  color: #f56c6c;
  font-weight: bold;
  font-size: 14px;
}
</style>
