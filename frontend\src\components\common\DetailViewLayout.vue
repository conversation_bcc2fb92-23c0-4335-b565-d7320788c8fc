<template>
  <div class="detail-view-layout">
    <!-- 1. 顶部区域：面包屑和标题 -->
    <div class="header">
      <div class="header-left">
        <el-page-header @back="goBack">
          <template #content>
            <span class="font-600"> {{ title }} </span>
          </template>
        </el-page-header>
      </div>
      <div class="header-right">
        <!-- 操作按钮插槽 -->
        <slot name="actions"></slot>
      </div>
    </div>

    <!-- 2. 主体内容区域 -->
    <div class="content-wrapper">
        <!-- 头部信息卡片插槽 -->
        <div class="header-info-card" v-if="$slots['header-info']">
             <slot name="header-info"></slot>
        </div>
        
        <!-- 默认插槽，用于放置 el-tabs -->
        <div class="main-content">
            <slot></slot>
        </div>
    </div>
  </div>
</template>

<script setup>
import { defineProps } from 'vue';
import { useRouter } from 'vue-router';

// -- Props --
defineProps({
  title: {
    type: String,
    default: '详情',
  },
});

// -- Router --
const router = useRouter();
const goBack = () => {
  router.back();
};
</script>

<style scoped>
.detail-view-layout {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 20px;
  background-color: #f5f7fa;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
  padding-bottom: 20px;
}

.header-left .el-page-header__content {
  font-size: 18px;
}

.content-wrapper {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  gap: 20px;
  overflow-y: auto;
}

.header-info-card, .main-content {
    background-color: #fff;
    padding: 20px;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
}

.main-content {
    flex-grow: 1;
}
</style> 