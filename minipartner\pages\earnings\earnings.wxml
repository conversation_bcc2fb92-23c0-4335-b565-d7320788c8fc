<view class="earnings-container">
  <!-- 加载状态 -->
  <view class="loading-mask" wx:if="{{loading}}">
    <view class="loading-icon"></view>
  </view>

  <!-- 顶部卡片区域 -->
  <view class="header-section">
    <!-- 收益概览卡片 -->
    <view class="overview-card">
      <view class="overview-header">
        <text class="overview-title">收益详情</text>
      </view>
      <view class="overview-content">
        <view class="overview-item">
          <text class="overview-label">待发放收益</text>
          <text class="overview-value">¥{{pending_earnings}}</text>
        </view>
        <view class="overview-divider"></view>
        <view class="overview-item">
          <text class="overview-label">已发放收益</text>
          <text class="overview-value">¥{{paid_earnings}}</text>
        </view>
        <view class="overview-divider"></view>
        <view class="overview-item">
          <text class="overview-label">累计总收益</text>
          <text class="overview-value total">¥{{total_earnings}}</text>
        </view>
      </view>
      <view class="overview-footer">
        <view class="settlement-info">
          <view class="settlement-icon"></view>
          <text class="settlement-text">每月10号统一结算上月收益，请注意查收</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 订单区域 -->
  <view class="orders-section">
    <view class="section-header">
      <text class="section-title">推广订单</text>
    </view>
    
    <!-- 筛选器区域 -->
    <view class="filter-area">
      <!-- 搜索栏和筛选图标 -->
      <view class="search-filter-row">
        <view class="search-wrapper">
          <view class="search-icon"></view>
          <input 
            class="search-input" 
            placeholder="搜索订单" 
            confirm-type="search"
            bindinput="onSearchInput"
            bindconfirm="onSearchConfirm"
            value="{{searchKeyword}}"
          />
        </view>
        
        <view class="filter-trigger" bindtap="toggleFilterPopup">
          <view class="filter-icon {{hasTimeFilter ? 'active' : ''}}">
            <view class="filter-dot" wx:if="{{hasTimeFilter}}"></view>
          </view>
          <text class="filter-text">筛选</text>
        </view>
      </view>
      
      <!-- 状态选项卡 -->
      <view class="status-tabs">
        <view 
          class="status-tab {{currentTab === 0 ? 'active' : ''}}" 
          bindtap="switchTab" 
          data-index="0"
        >全部({{orderCounts.total || 0}})</view>
        <view 
          class="status-tab {{currentTab === 1 ? 'active' : ''}}" 
          bindtap="switchTab" 
          data-index="1"
        >待发放({{orderCounts.pending || 0}})</view>
        <view 
          class="status-tab {{currentTab === 2 ? 'active' : ''}}" 
          bindtap="switchTab" 
          data-index="2"
        >已发放({{orderCounts.paid || 0}})</view>
      </view>
    </view>
    
    <!-- 订单列表 -->
    <view class="order-list">
      <block wx:if="{{orderList.length > 0}}">
        <view class="order-card" wx:for="{{orderList}}" wx:key="orderId">
          <view class="order-header">
            <view class="order-number">
              <text class="order-label">订单号</text>
              <text class="order-id">{{item.orderId}}</text>
            </view>
            <text class="order-date">{{item.createTime}}</text>
          </view>
          
          <view class="order-content">
            <view class="product-info">
              <text class="product-name">{{item.productName}}</text>
              <text class="product-version" wx:if="{{item.version}}">{{item.version}}</text>
              <text class="company-name" wx:if="{{item.companyName}}">{{item.companyName}}</text>
            </view>
            
            <view class="price-details">
              <view class="price-row">
                <text class="price-label">标准金额</text>
                <text class="price-value">¥{{item.standardPrice}}</text>
              </view>
              <view class="price-row">
                <text class="price-label">实付金额</text>
                <text class="price-value">¥{{item.actualPrice}}</text>
              </view>
              <view class="price-row">
                <text class="price-label">佣金比例</text>
                <text class="price-value highlight">{{item.commissionRate}}%</text>
              </view>
            </view>
          </view>
          
          <view class="order-footer">
            <view class="commission-info">
              <text class="commission-label">佣金金额</text>
              <text class="commission-value">¥{{item.commission || '0.00'}}</text>
            </view>
            <view class="status-badge {{item.commissionStatus === '已发放' ? 'paid' : 'pending'}}">
              {{item.commissionStatus || '未发放'}}
            </view>
          </view>
        </view>
      </block>
      
      <!-- 空状态 -->
      <view class="empty-state" wx:else>
        <view class="empty-icon"></view>
        <text class="empty-text">暂无订单数据</text>
      </view>
    </view>
    
    <!-- 底部提示 -->
    <view class="list-footer" wx:if="{{orderList.length > 0}}">
      <text class="footer-text">— 已显示全部{{currentTab === 0 ? '' : currentTab === 1 ? '待结算' : '已结算'}}订单 —</text>
    </view>
  </view>
</view>

<!-- 筛选弹窗 -->
<view class="filter-modal {{showFilterPopup ? 'visible' : ''}}">
  <view class="modal-backdrop" bindtap="toggleFilterPopup"></view>
  <view class="modal-container">
    <view class="modal-header">
      <text class="modal-title">订单筛选</text>
      <view class="close-button" bindtap="toggleFilterPopup"></view>
    </view>
    
    <view class="modal-body">
      <view class="filter-group">
        <text class="filter-group-title">下单时间</text>
        <view class="time-options">
          <view 
            class="time-option {{timeFilterType === 'recent1m' ? 'active' : ''}}" 
            bindtap="selectTimeFilterType" 
            data-type="recent1m"
          >近1个月</view>
          <view 
            class="time-option {{timeFilterType === 'recent3m' ? 'active' : ''}}" 
            bindtap="selectTimeFilterType" 
            data-type="recent3m"
          >近3个月</view>
          <view 
            class="time-option {{timeFilterType === 'recent6m' ? 'active' : ''}}" 
            bindtap="selectTimeFilterType" 
            data-type="recent6m"
          >近6个月</view>
        </view>
      </view>
      
      <view class="filter-group">
        <text class="filter-group-title">自定义时间范围</text>
        <view class="date-range">
          <view class="date-field">
            <text class="date-label">起始时间</text>
            <picker mode="date" value="{{startDate}}" bindchange="onStartDateChange">
              <view class="date-picker {{startDate ? 'has-value' : ''}}">
                {{startDate || '选择日期'}}
              </view>
            </picker>
          </view>
          <view class="date-field">
            <text class="date-label">结束时间</text>
            <picker mode="date" value="{{endDate}}" bindchange="onEndDateChange">
              <view class="date-picker {{endDate ? 'has-value' : ''}}">
                {{endDate || '选择日期'}}
              </view>
            </picker>
          </view>
        </view>
      </view>
    </view>
    
    <view class="modal-footer">
      <button class="reset-button" bindtap="resetFilters">重置</button>
      <button class="confirm-button" bindtap="applyFilters">确定</button>
    </view>
  </view>
</view> 