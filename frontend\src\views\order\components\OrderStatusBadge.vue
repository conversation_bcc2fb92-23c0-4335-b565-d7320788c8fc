<template>
  <div class="order-status-badge">
    <!-- 审核状态 -->
    <div class="status-item">
      <span class="status-label">审核状态：</span>
      <el-tag
        :type="auditStatusType"
        size="large"
        effect="dark"
        class="status-tag"
      >
        {{ auditStatusText }}
      </el-tag>
    </div>

    <!-- 支付状态 -->
    <div class="status-item" v-if="showPaymentStatus">
      <span class="status-label">支付状态：</span>
      <el-tag
        :type="paymentStatusType"
        size="large"
        effect="dark"
        class="status-tag"
      >
        {{ paymentStatus }}
      </el-tag>
    </div>

    <!-- 佣金状态 -->
    <div class="status-item" v-if="showCommissionStatus && commissionStatus">
      <span class="status-label">佣金状态：</span>
      <el-tag
        :type="commissionStatusType"
        size="large"
        effect="dark"
        class="status-tag"
      >
        {{ commissionStatus }}
      </el-tag>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';

const props = defineProps({
  // 审核状态
  auditStatus: {
    type: String,
    default: '待审核'
  },
  // 支付状态
  paymentStatus: {
    type: String,
    default: ''
  },
  // 佣金状态（预留）
  commissionStatus: {
    type: String,
    default: ''
  },
  // 是否显示支付状态
  showPaymentStatus: {
    type: Boolean,
    default: true
  },
  // 是否显示佣金状态
  showCommissionStatus: {
    type: Boolean,
    default: false
  }
});

// 审核状态文本和类型
const auditStatusText = computed(() => {
  switch (props.auditStatus) {
    case '待审核':
      return '未审核';
    case '已审核':
      return '已审核';
    default:
      return props.auditStatus;
  }
});

const auditStatusType = computed(() => {
  switch (props.auditStatus) {
    case '待审核':
      return 'warning';
    case '已审核':
      return 'success';
    default:
      return 'info';
  }
});

// 支付状态类型
const paymentStatusType = computed(() => {
  switch (props.paymentStatus) {
    case '已支付':
      return 'success';
    case '未支付':
      return 'warning';
    case '已退款':
      return 'danger';
    default:
      return 'info';
  }
});

// 佣金状态类型
const commissionStatusType = computed(() => {
  console.log('OrderStatusBadge 佣金状态计算属性被触发:', {
    commissionStatus: props.commissionStatus,
    showCommissionStatus: props.showCommissionStatus,
    timestamp: new Date().toISOString()
  });

  switch (props.commissionStatus) {
    case '已发放':
      return 'success';
    case '未发放':
      return 'warning';
    default:
      return 'info';
  }
});
</script>

<style scoped>
.order-status-badge {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  padding: 16px 20px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border: 2px solid #dee2e6;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.status-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-label {
  font-size: 14px;
  color: #495057;
  font-weight: 600;
  white-space: nowrap;
}

.status-tag {
  font-weight: 600;
  font-size: 13px;
  padding: 6px 12px;
  border-radius: 6px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}
</style>
