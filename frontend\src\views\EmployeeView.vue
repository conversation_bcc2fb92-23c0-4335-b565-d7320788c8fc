<script setup>
import { ref, computed } from 'vue';
import CrudPage from '@/components/common/CrudPage.vue';
import { getEmployees, createEmployee, updateEmployee, deleteEmployee, getNextEmployeeNumber } from '@/api/employee.js';
import { verifyPassword } from '@/api/auth.js';
import { ElMessage, ElMessageBox } from 'element-plus';
import { useAuth } from '@/store/auth';

// 1. 定义表格列
const columns = ref([
  { prop: 'id', label: 'ID', width: 80 },
  { prop: 'employee_number', label: '员工工号', width: 150 },
  { prop: 'name', label: '姓名', width: 150 },
  { prop: 'mobile', label: '手机号', width: 180 },
  { prop: 'department', label: '部门', width: 180 },
  { prop: 'role', label: '角色', width: 120, isSlot: true },
  { prop: 'remark', label: '备注', showOverflowTooltip: true },
]);

// 2. 定义API
const api = {
  list: getEmployees,
  create: createEmployee,
  update: updateEmployee,
  delete: deleteEmployee,
};

// 3. 状态管理
const dialogVisible = ref(false);
const isEditMode = ref(false);
const crudPageRef = ref(null);
const selectedItems = ref([]);
const submitting = ref(false);

// 3.1 权限和表单状态 (完全保留)
const { state: authState } = useAuth();
const isEditingSelf = computed(() => isEditMode.value && form.value.id === authState.user?.id);

const getInitialForm = () => ({
  id: null, employee_number: '', name: '', mobile: '', department: '',
  password: '', role: 'employee', remark: ''
});
const form = ref(getInitialForm());


// 4. 计算属性，控制按钮禁用状态
const isEditDisabled = computed(() => selectedItems.value.length !== 1);
const isCopyDisabled = computed(() => selectedItems.value.length !== 1);
const isDeleteDisabled = computed(() => selectedItems.value.length === 0);

// 5. 方法
const handleSelectionChange = (selection) => {
  selectedItems.value = selection;
};

const handleOpenDialog = async (editMode = false, copyMode = false, rowData = null) => {
  isEditMode.value = editMode;
  if (editMode && rowData) {
    form.value = { ...rowData, password: '' };
  } else if (copyMode && rowData) {
    const copiedData = { ...rowData, password: '' };
    delete copiedData.id;
    try {
      const res = await getNextEmployeeNumber();
      copiedData.employee_number = res.next_id;
    } catch (error) {
      ElMessage.error('获取新员工工号失败，请为复制的记录输入一个唯一的工号。');
      copiedData.employee_number = '';
    }
    form.value = copiedData;
    isEditMode.value = false;
  } else {
    form.value = getInitialForm();
    try {
      const res = await getNextEmployeeNumber();
      form.value.employee_number = res.next_id;
    } catch (error) {
      ElMessage.error('获取新员工工号失败，请输入一个唯一的工号。');
    }
  }
  dialogVisible.value = true;
};

const handleBatchDelete = async () => {
  if (isDeleteDisabled.value) return;
  const idsToDelete = selectedItems.value.map(item => item.id);
  await ElMessageBox.confirm(`确定删除选中的 ${idsToDelete.length} 位员工吗？`, '警告', { type: 'warning' });
  try {
    await Promise.all(idsToDelete.map(id => api.delete(id)));
    ElMessage.success('删除成功');
    crudPageRef.value?.loadData();
    selectedItems.value = [];
  } catch (error) {
    ElMessage.error('删除失败');
  }
};

// 6. 提交逻辑 (完全保留)
const handleSubmit = async () => {
  if (submitting.value) return;
  const needsReauthentication = isEditMode.value && form.value.password && !isEditingSelf.value;
  try {
    submitting.value = true;
    if (needsReauthentication) {
      const { value } = await ElMessageBox.prompt('此操作为敏感操作，请输入您的登录密码以确认：', '安全确认', {
        confirmButtonText: '确认', cancelButtonText: '取消', inputType: 'password',
      });
      await verifyPassword(value);
    }
    await proceedToSave();
  } catch (error) {
    if (error !== 'cancel') ElMessage.error('操作失败，请检查');
  } finally {
    submitting.value = false;
  }
};

const proceedToSave = async () => {
  const dataToSend = { ...form.value };
  if (!dataToSend.password) delete dataToSend.password;

  if (isEditMode.value) {
    await api.update(form.value.id, dataToSend);
    ElMessage.success('更新成功');
  } else {
    if (!dataToSend.password) {
      ElMessage.error('新增员工时必须设置密码');
      throw new Error("Password is required for new employee");
    }
    await api.create(dataToSend);
    ElMessage.success('创建成功');
  }
  dialogVisible.value = false;
  crudPageRef.value?.loadData();
};
</script>

<template>
  <CrudPage
    ref="crudPageRef"
    title="员工"
    :columns="columns"
    :api-list="api.list"
    :api-create="api.create"
    :api-update="api.update"
    :api-delete="api.delete"
    :hide-row-actions="true"
    @selection-change="handleSelectionChange"
  >
    <!-- 1. 自定义顶部工具栏 -->
    <template #actions>
      <el-button type="primary" @click="handleOpenDialog(false, false, null)">新增员工</el-button>
       <el-tooltip content="请选择一位员工进行修改" :disabled="!isEditDisabled" placement="top">
        <div style="display: inline-block; margin: 0 6px;"><el-button :disabled="isEditDisabled" @click="handleOpenDialog(true, false, selectedItems[0])">修改</el-button></div>
      </el-tooltip>
      <el-tooltip content="请选择一位员工进行复制" :disabled="!isCopyDisabled" placement="top">
        <div style="display: inline-block; margin: 0 6px;"><el-button type="success" :disabled="isCopyDisabled" @click="handleOpenDialog(false, true, selectedItems[0])">复制</el-button></div>
      </el-tooltip>
      <el-tooltip content="请至少选择一位员工进行删除" :disabled="!isDeleteDisabled" placement="top">
        <div style="display: inline-block; margin: 0 6px;"><el-button type="danger" :disabled="isDeleteDisabled" @click="handleBatchDelete">删除</el-button></div>
      </el-tooltip>
    </template>
    
    <!-- 2. 自定义表格列 -->
    <template #col-role="{ row }">
      <el-tag :type="row.role === 'admin' ? 'success' : 'info'">
        {{ row.role === 'admin' ? '管理员' : '普通员工' }}
      </el-tag>
    </template>

    <!-- 3. 自定义弹窗 (完全保留旧代码) -->
    <template #dialog>
      <el-dialog v-model="dialogVisible" :title="isEditMode ? '编辑员工' : '新增员工'" width="500px" :close-on-click-modal="false">
        <el-form :model="form" label-width="80px">
          <el-form-item label="工号"><el-input v-model="form.employee_number"></el-input></el-form-item>
          <el-form-item label="姓名"><el-input v-model="form.name"></el-input></el-form-item>
          <el-form-item label="手机号"><el-input v-model="form.mobile"></el-input></el-form-item>
          <el-form-item label="部门"><el-input v-model="form.department"></el-input></el-form-item>
          <el-form-item label="角色">
            <el-select v-model="form.role" placeholder="请选择员工角色">
              <el-option label="普通员工" value="employee"></el-option>
              <el-option label="管理员" value="admin"></el-option>
            </el-select>
          </el-form-item>
          <template v-if="!isEditingSelf">
            <el-form-item label="密码"><el-input v-model="form.password" type="password" show-password :placeholder="isEditMode ? '留空则不修改密码' : '请输入密码'"></el-input></el-form-item>
          </template>
          <template v-else>
            <el-form-item label="密码"><el-alert title="请在右上角的用户菜单中修改您自己的密码" type="info" :closable="false" show-icon /></el-form-item>
          </template>
          <el-form-item label="备注"><el-input v-model="form.remark" type="textarea"></el-input></el-form-item>
        </el-form>
        <template #footer>
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitting">提交</el-button>
        </template>
      </el-dialog>
    </template>
  </CrudPage>
</template>

<style scoped>
.page-container {
  padding: 20px;
}
.action-bar {
  margin-bottom: 20px;
}
</style> 