const express = require('express');
const {
  getAllEnterprises,
  createEnterprise,
  getEnterpriseById,
  updateEnterprise,
  deleteEnterprise,
  downloadLicense,
  getNextEnterpriseId,
  getOrdersByEnterprise
} = require('../controllers/enterprise.controller');
const { employee } = require('../middleware/auth');
const { verifyToken, requireUserType } = require('../middleware/auth/base');
const upload = require('../middleware/upload'); // 引入 multer 中间件
const { checkOwnership } = require('../middleware/permissionChecks');
const db = require('../models');
const Enterprise = db.Enterprise;

const router = express.Router();

// [新增] 获取下一个可用ID，确保此路由在带:id的路由之前
router.get('/next-id', employee.verifyEmployee, getNextEnterpriseId);

// GET /api/enterprises - 获取企业列表（员工获取所有企业，用户获取关联企业）
router.get('/', [verifyToken, requireUserType(['employee', 'user'])], getAllEnterprises);

// POST /api/enterprises - 创建一个新企业
router.post('/', [employee.verifyEmployee, upload.single('license_image')], createEnterprise);

// PUT /api/enterprises/:id - 更新ID为:id的单个企业
router.put('/:id', [employee.verifyEmployee, checkOwnership(Enterprise), upload.single('license_image')], updateEnterprise);

// DELETE /api/enterprises/:id - 删除ID为:id的单个企业
router.delete('/:id', [employee.verifyEmployee, checkOwnership(Enterprise)], deleteEnterprise);

// [新增] 下载指定ID企业的营业执照
router.get('/:id/license/download', employee.verifyEmployee, downloadLicense);

// [新增] 获取指定企业的关联订单
router.get('/:id/orders', employee.verifyEmployee, getOrdersByEnterprise);

module.exports = router;