<script setup>
import { ref, watch, defineProps, defineEmits, computed } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { getFollowupsByEnterpriseId, createFollowup, updateFollowup, deleteFollowup } from '@/api/followup.js';
import { getEmployees } from '@/api/employee.js';
import { useAuth } from '@/store/auth.js';
import { formatDateTime } from '@/utils/format.js';
import service from '@/utils/request_extra.js'; // [新增] 导入axios实例

// -- Props and Emits --
const props = defineProps({
  visible: {
    type: Boolean,
    required: true,
  },
  enterprise: {
    type: Object,
    required: true,
  },
});

const emit = defineEmits(['update:visible', 'submitted']);

// -- Auth and State --
const { state: authState } = useAuth();
const isAdmin = computed(() => authState.user?.role === 'admin');

// -- Component-Specific State --
const followupLoading = ref(false);
const followups = ref([]);
const employeesList = ref([]);

const getInitialFollowupForm = () => ({
  followup_time: new Date(),
  situation: '',
  employee_id: authState.user?.id ?? null, // Default to current user
  remark: '',
});

const newFollowup = ref(getInitialFollowupForm());
const followupFile = ref(null);
const isFollowupEditMode = ref(false);

// -- Methods --
const loadFollowups = async () => {
  if (!props.enterprise?.id) return;
  try {
    followupLoading.value = true;
    const response = await getFollowupsByEnterpriseId(props.enterprise.id);
    followups.value = response;
  } catch (error) {
    ElMessage.error('获取跟进记录失败');
  } finally {
    followupLoading.value = false;
  }
};

const loadEmployees = async () => {
    try {
        employeesList.value = await getEmployees();
    } catch(e) {
        ElMessage.error('获取员工列表失败');
    }
}

const handleEditFollowup = (followup) => {
  newFollowup.value = JSON.parse(JSON.stringify(followup));
  isFollowupEditMode.value = true;
  ElMessage.info('记录已填充至下方表单，请修改后提交。');
};

const handleFileChange = (file) => {
  followupFile.value = file.raw;
};

const handleFollowupSubmit = async () => {
  if (!newFollowup.value.situation || !newFollowup.value.employee_id) {
    ElMessage.warning('请填写跟进情况和选择跟进员工');
    return;
  }
  try {
    const formData = new FormData();
    const followupTime = newFollowup.value.followup_time instanceof Date 
      ? newFollowup.value.followup_time.toISOString()
      : new Date(newFollowup.value.followup_time).toISOString();

    formData.append('enterprise_id', props.enterprise.id);
    formData.append('followup_time', followupTime);
    formData.append('situation', newFollowup.value.situation);
    formData.append('employee_id', newFollowup.value.employee_id);
    formData.append('remark', newFollowup.value.remark || '');

    if (followupFile.value) {
      formData.append('attachment', followupFile.value);
    }
    
    if (isFollowupEditMode.value) {
      await updateFollowup(newFollowup.value.id, formData);
      ElMessage.success('修改跟进记录成功');
    } else {
      await createFollowup(formData);
      ElMessage.success('添加跟进记录成功');
    }
    
    resetForm();
    await loadFollowups();
    emit('submitted'); // Notify parent that data has changed
  } catch (error) {
    ElMessage.error((isFollowupEditMode.value ? '修改' : '添加') + '失败: ' + (error.response?.data?.message || error.message));
  }
};

const handleDeleteFollowup = (followupId) => {
  ElMessageBox.confirm('您确定要删除这条跟进记录吗？', '警告', {
    confirmButtonText: '确定删除',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    try {
      await deleteFollowup(followupId);
      ElMessage.success('删除成功');
      await loadFollowups();
       emit('submitted'); // Notify parent that data has changed
    } catch (error) {
      ElMessage.error('删除失败');
    }
  }).catch(() => {
    ElMessage.info('已取消删除');
  });
};

const handleCloseDialog = () => {
  emit('update:visible', false);
};

const resetForm = () => {
    isFollowupEditMode.value = false;
    newFollowup.value = getInitialFollowupForm();
    followupFile.value = null;
}

// [新增] 处理跟进记录附件下载的方法
const handleDownloadAttachment = (row) => {
  if (!row.id || !row.attachment) {
    ElMessage.warning('没有可供下载的附件');
    return;
  }
  const baseURL = service.defaults.baseURL || '';
  const token = localStorage.getItem('authToken');
  const downloadUrl = `${baseURL}/followups/${row.id}/attachment/download?token=${token}`;
  window.open(downloadUrl, '_blank');
};


// -- Watchers --
watch(() => props.enterprise, (newEnterprise) => {
  if (newEnterprise && newEnterprise.id) {
    resetForm();
    loadFollowups();
    loadEmployees();
  }
}, { immediate: true });

</script>

<template>
  <el-dialog 
    title="跟进记录" 
    :model-value="visible" 
    width="65%" 
    @update:modelValue="handleCloseDialog"
    >
    <div v-if="enterprise" style="margin-bottom: 20px;">
      <strong>当前企业: {{ enterprise.name }}</strong>
    </div>
    <el-table :data="followups" v-loading="followupLoading" border>
      <el-table-column prop="id" label="记录ID" width="80"></el-table-column>
      <el-table-column label="跟进时间" width="200">
        <template #default="scope">
          {{ formatDateTime(scope.row.followup_time) }}
        </template>
      </el-table-column>
      <el-table-column prop="situation" label="跟进情况"></el-table-column>
      <el-table-column label="附件" width="150">
        <template #default="scope">
          <el-button
            v-if="scope.row.attachment"
            type="primary"
            link
            @click="handleDownloadAttachment(scope.row)"
          >
            下载附件
          </el-button>
          <span v-else>无</span>
        </template>
      </el-table-column>
      <el-table-column label="跟进员工" width="120">
        <template #default="scope">
          {{ scope.row.employee ? scope.row.employee.name : 'N/A' }}
        </template>
      </el-table-column>
      <el-table-column prop="remark" label="备注"></el-table-column>
      <el-table-column label="操作" width="180">
        <template #default="scope">
          <el-button size="small" @click="handleEditFollowup(scope.row)">修改</el-button>
          <el-button size="small" type="danger" @click="handleDeleteFollowup(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-divider></el-divider>

    <h4>{{ isFollowupEditMode ? '修改跟进记录' : '新增跟进记录' }}</h4>
    <el-form :model="newFollowup" label-width="100px">
      <el-row>
        <el-col :span="12">
          <el-form-item label="跟进时间">
            <el-date-picker
              v-model="newFollowup.followup_time"
              type="datetime"
              placeholder="选择日期时间"
              style="width: 100%;"
            ></el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="跟进员工">
            <el-select v-model="newFollowup.employee_id" placeholder="选择员工" clearable filterable :disabled="!isAdmin">
              <el-option
                v-for="item in employeesList"
                :key="item.id"
                :label="item.name"
                :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item label="跟进情况">
        <el-input v-model="newFollowup.situation" type="textarea" :rows="3"></el-input>
      </el-form-item>
      <el-form-item label="附件上传">
        <el-upload
          :auto-upload="false"
          :on-change="handleFileChange"
          :limit="1"
        >
          <template #trigger>
            <el-button type="primary">选择文件</el-button>
          </template>
        </el-upload>
      </el-form-item>
      <el-form-item label="备注">
        <el-input v-model="newFollowup.remark" type="textarea"></el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <div style="text-align: right;">
        <el-button @click="handleCloseDialog">取消</el-button>
        <el-button type="primary" @click="handleFollowupSubmit">提交</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style scoped>
/* Scoped styles for this component */
</style> 