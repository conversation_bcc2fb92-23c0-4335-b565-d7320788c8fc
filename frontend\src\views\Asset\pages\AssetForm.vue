<template>
  <div class="asset-form-page" v-loading="loading">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <el-button @click="handleBack" size="default" class="back-button">
            <el-icon><ArrowLeft /></el-icon>
            返回
          </el-button>
          <h2 class="page-title">{{ pageTitle }}</h2>
        </div>
        
        <div class="header-actions">
          <el-button type="primary" @click="handleSave" :loading="saving">
            <el-icon><Check /></el-icon>
            保存
          </el-button>
          <el-button @click="handleCancel">
            <el-icon><Close /></el-icon>
            取消
          </el-button>
        </div>
      </div>
    </div>

    <!-- 主要内容 -->
    <div class="form-content" v-if="formData">
      <!-- 资产表单表头 -->
      <AssetFormHeader
        :formData="formData"
        :readonly="false"
        :enterpriseOptions="enterpriseOptions"
        :userOptions="userOptions"
        @enterprise-change="handleEnterpriseChange"
        @user-change="handleUserChange"
        ref="headerFormRef"
      />

      <!-- 标签页内容 -->
      <div class="tabs-section">
        <el-tabs v-model="activeTab" class="form-tabs" type="border-card">
          <!-- 产品详情 -->
          <el-tab-pane name="product">
            <template #label>
              <span class="tab-label">
                <el-icon><Box /></el-icon>
                产品详情
              </span>
            </template>
            <AssetProductDetail
              :formData="formData"
              :readonly="false"
              :productOptions="productOptions"
              @product-change="handleProductChange"
              @data-change="handleDataChange"
              ref="productFormRef"
            />
          </el-tab-pane>

          <!-- 激活信息 -->
          <el-tab-pane name="activation">
            <template #label>
              <span class="tab-label">
                <el-icon><Key /></el-icon>
                激活信息
              </span>
            </template>
            <AssetActivationInfo
              :formData="formData"
              :readonly="false"
              @data-change="handleDataChange"
              ref="activationFormRef"
            />
          </el-tab-pane>

          <!-- 关联订单（仅新增时显示） -->
          <el-tab-pane name="orders" v-if="isCreateMode">
            <template #label>
              <span class="tab-label">
                <el-icon><Document /></el-icon>
                关联订单
              </span>
            </template>
            <AssetRelatedOrders
              mode="create"
              :readonly="false"
              :enterpriseId="formData.enterprise_id"
              :selectedOrderIds="selectedOrderIds"
              @orders-change="handleOrdersChange"
            />
          </el-tab-pane>
        </el-tabs>
      </div>

      <!-- 表尾信息 -->
      <div class="footer-section">
        <el-card shadow="never" class="footer-card">
          <template #header>
            <span class="card-header">其他信息</span>
          </template>
          
          <el-form :model="formData" label-width="120px">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="备注">
                  <el-input
                    v-model="formData.remark"
                    type="textarea"
                    :rows="4"
                    placeholder="请输入备注信息"
                    maxlength="500"
                    show-word-limit
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="制单人">
                  <el-input :value="getCreatorName()" readonly />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="制单时间">
                  <el-input :value="formatDateTime(formData.createdAt)" readonly />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { 
  ArrowLeft, Check, Close, Box, Key, Document
} from '@element-plus/icons-vue'

// 组件导入
import AssetFormHeader from '../components/AssetFormHeader.vue'
import AssetProductDetail from '../components/AssetProductDetail.vue'
import AssetActivationInfo from '../components/AssetActivationInfo.vue'
import AssetRelatedOrders from '../components/AssetRelatedOrders.vue'

// 工具函数
import { formatDateTime } from '@/utils/format.js'

// Composables
import { useAssetData } from '../composables/useAssetData.js'
import { useAuth } from '@/store/auth.js'

// API
import { getEnterprises } from '@/api/enterprise.js'
import { getUsers } from '@/api/user.js'
import { getProducts } from '@/api/product.js'
import { getNextAssetId } from '@/api/asset.js'

// 路由
const route = useRoute()
const router = useRouter()

// 数据管理
const {
  loading,
  saving,
  fetchAsset,
  createNewAsset,
  updateExistingAsset
} = useAssetData()

// 认证状态
const { state: authState } = useAuth()

// 状态
const activeTab = ref('product')
const formData = ref(null)
const selectedOrderIds = ref([])

// 表单引用
const headerFormRef = ref(null)
const productFormRef = ref(null)
const activationFormRef = ref(null)

// 选项数据
const enterpriseOptions = ref([])
const userOptions = ref([])
const productOptions = ref([])

// 计算属性
const assetId = computed(() => route.params.id || null)
const isCreateMode = computed(() => route.query.mode === 'add' || !assetId.value)
const pageTitle = computed(() => {
  return isCreateMode.value ? '新增资产' : '修改资产'
})

// 加载选项数据
const loadOptions = async () => {
  try {
    const [enterprises, users, products] = await Promise.all([
      getEnterprises(),
      getUsers(),
      getProducts()
    ])

    enterpriseOptions.value = enterprises || []
    userOptions.value = users || []
    productOptions.value = products || []
  } catch (error) {
    console.error('加载选项数据失败:', error)
    ElMessage.error('加载选项数据失败')
  }
}

// 初始化表单数据
const initializeFormData = async () => {
  if (isCreateMode.value) {
    // 新增模式：初始化空表单
    try {
      const response = await getNextAssetId()
      const nextId = response.next_id || response
      formData.value = {
        asset_id: nextId,
        enterprise_id: null,
        user_id: null,
        product_id: null,
        user_count: 1,
        account_count: 1,
        duration_months: 12,
        selected_features: [],
        purchase_date: new Date(),
        product_expiry_date: null,
        sps_expiry_date: null,
        after_sales_expiry_date: null,
        product_standard_price: 0,
        sps_annual_fee: 0,
        after_sales_service_fee: 0,
        implementation_fee: 0,
        activation_code: '',
        activation_phone: '',
        activation_password: '',
        remark: '',
        status: '在线',
        creator: authState.user,
        createdAt: new Date()
      }
    } catch (error) {
      console.error('初始化表单数据失败:', error)
      ElMessage.error('初始化表单数据失败')
    }
  } else {
    // 编辑模式：加载现有数据
    try {
      const data = await fetchAsset(assetId.value)
      // 确保数字字段是正确的数字类型
      formData.value = {
        ...data,
        user_count: Number(data.user_count || 1),
        account_count: Number(data.account_count || 1),
        duration_months: Number(data.duration_months || 12),
        product_standard_price: Number(data.product_standard_price || 0),
        sps_annual_fee: Number(data.sps_annual_fee || 0),
        after_sales_service_fee: Number(data.after_sales_service_fee || 0),
        implementation_fee: Number(data.implementation_fee || 0)
      }
    } catch (error) {
      console.error('加载资产数据失败:', error)
      ElMessage.error('加载资产数据失败')
    }
  }
}

// 获取制单人姓名
const getCreatorName = () => {
  if (formData.value?.creator) {
    return formData.value.creator.name || formData.value.creator.employee_name || '未知'
  }
  if (authState.user) {
    return authState.user.name || authState.user.employee_name || '当前用户'
  }
  return '未知'
}

// 事件处理
const handleBack = () => {
  router.back()
}

const handleCancel = () => {
  router.push({ name: 'asset-list' })
}

const handleEnterpriseChange = (enterpriseId) => {
  // 当企业变更时，清空用户选择
  if (formData.value.user_id) {
    const currentUser = userOptions.value.find(user => user.id === formData.value.user_id)
    if (!currentUser || currentUser.enterprise_id !== enterpriseId) {
      formData.value.user_id = null
    }
  }
}

const handleUserChange = (userId) => {
  // 用户变更处理
}

const handleProductChange = (productId) => {
  // 产品变更处理
}

const handleDataChange = () => {
  // 数据变更处理
}

const handleOrdersChange = (orderIds) => {
  selectedOrderIds.value = orderIds
}

// 表单验证
const validateForm = async () => {
  try {
    const headerValid = await headerFormRef.value?.validate()
    const productValid = await productFormRef.value?.validate()
    const activationValid = await activationFormRef.value?.validate()
    
    return headerValid && productValid && activationValid
  } catch (error) {
    return false
  }
}

// 保存处理
const handleSave = async () => {
  // 验证表单
  const isValid = await validateForm()
  if (!isValid) {
    ElMessage.error('请检查表单数据')
    return
  }

  try {
    const saveData = {
      ...formData.value,
      order_ids: selectedOrderIds.value // 关联的订单ID列表
    }

    if (isCreateMode.value) {
      await createNewAsset(saveData)
      ElMessage.success('资产创建成功')
    } else {
      await updateExistingAsset(assetId.value, saveData)
      ElMessage.success('资产更新成功')
    }

    // 跳转到资产列表
    router.push({ name: 'asset-list' })
  } catch (error) {
    console.error('保存失败:', error)
  }
}

// 生命周期
onMounted(async () => {
  await loadOptions()
  await initializeFormData()
})
</script>

<style scoped>
.asset-form-page {
  padding: 24px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

/* 页面头部 */
.page-header {
  margin-bottom: 24px;
  padding: 20px 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
  color: white;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.back-button {
  border: 1px solid rgba(255, 255, 255, 0.3);
  background: rgba(255, 255, 255, 0.1);
  color: white;
  backdrop-filter: blur(10px);
}

.back-button:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.5);
}

.page-title {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: white;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.header-actions .el-button {
  border: 1px solid rgba(255, 255, 255, 0.3);
  background: rgba(255, 255, 255, 0.1);
  color: white;
  backdrop-filter: blur(10px);
}

.header-actions .el-button:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.5);
}

.header-actions .el-button--primary {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
}

/* 表单内容 */
.form-content {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* 标签页 */
.tabs-section {
  margin: 20px 0;
}

.form-tabs {
  background: white;
}

.tab-label {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 表尾信息 */
.footer-section {
  margin-top: 20px;
}

.footer-card {
  border-radius: 8px;
}

.card-header {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .asset-form-page {
    padding: 16px;
  }
  
  .page-header {
    padding: 16px;
  }
  
  .header-content {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }
  
  .page-title {
    font-size: 20px;
  }
}
</style>
