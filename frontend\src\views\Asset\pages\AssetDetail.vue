<template>
  <div class="asset-detail-page" v-loading="loading">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <el-button @click="handleBack" size="default" class="back-button">
            <el-icon><ArrowLeft /></el-icon>
            返回
          </el-button>
          <h2 class="page-title">{{ pageTitle }}</h2>
        </div>

        <div class="header-actions">
          <el-button type="primary" @click="handleEdit" v-if="!isEditMode">
            <el-icon><Edit /></el-icon>
            修改
          </el-button>
          <el-button type="warning" @click="handleChange" v-if="!isEditMode">
            <el-icon><Switch /></el-icon>
            变更
          </el-button>
          <el-button type="success" @click="handleRenewal" v-if="!isEditMode">
            <el-icon><Money /></el-icon>
            续费
          </el-button>
          <el-button type="danger" @click="handleDelete" v-if="!isEditMode">
            <el-icon><Delete /></el-icon>
            删除
          </el-button>

          <!-- 编辑模式下的操作按钮 -->
          <el-button type="primary" @click="handleSave" :loading="saving" v-if="isEditMode">
            <el-icon><Check /></el-icon>
            保存
          </el-button>
          <el-button @click="handleCancel" v-if="isEditMode">
            <el-icon><Close /></el-icon>
            取消
          </el-button>
        </div>
      </div>
    </div>

    <!-- 主要内容 -->
    <div class="asset-detail-content" v-if="assetData">
      <!-- 资产表单表头 -->
      <AssetFormHeader
        :formData="assetData"
        :readonly="!isEditMode"
        :enterpriseOptions="enterpriseOptions"
        :userOptions="userOptions"
        @enterprise-change="handleEnterpriseChange"
        @user-change="handleUserChange"
        ref="headerFormRef"
      />

      <!-- 标签页内容 - 表体信息 -->
      <div class="tabs-section">
        <el-tabs v-model="activeTab" class="detail-tabs" type="border-card">
          <!-- 产品详情 -->
          <el-tab-pane name="product">
            <template #label>
              <span class="tab-label">
                <el-icon><Box /></el-icon>
                产品详情
              </span>
            </template>
            <AssetProductDetail
              v-if="assetData"
              :formData="assetData"
              :readonly="!isEditMode"
              :productOptions="productOptions"
              @product-change="handleProductChange"
              @data-change="handleDataChange"
              ref="productFormRef"
            />
          </el-tab-pane>

          <!-- 激活信息 -->
          <el-tab-pane name="activation">
            <template #label>
              <span class="tab-label">
                <el-icon><Key /></el-icon>
                激活信息
              </span>
            </template>
            <AssetActivationInfo
              v-if="assetData"
              :formData="assetData"
              :readonly="!isEditMode"
              @data-change="handleDataChange"
              ref="activationFormRef"
            />
          </el-tab-pane>

          <!-- 关联订单 -->
          <el-tab-pane name="orders">
            <template #label>
              <span class="tab-label">
                <el-icon><Document /></el-icon>
                关联订单
              </span>
            </template>
            <AssetRelatedOrders
              v-if="assetData"
              mode="view"
              :readonly="true"
              :relatedOrders="assetData.orders || []"
            />
          </el-tab-pane>

          <!-- 变更记录 -->
          <el-tab-pane name="changes" v-if="!isCreateMode">
            <template #label>
              <span class="tab-label">
                <el-icon><Clock /></el-icon>
                变更记录
              </span>
            </template>
            <AssetChangeRecords
              v-if="assetData && assetData.id"
              :assetId="assetData.id"
              :readonly="false"
              @rollback-success="handleLogReverted"
            />
          </el-tab-pane>
        </el-tabs>
      </div>

      <!-- 表尾信息 -->
      <div class="footer-section">
        <el-card shadow="never" class="footer-card">
          <template #header>
            <span class="card-header">其他信息</span>
          </template>

          <el-form :model="assetData" label-width="120px" :disabled="!isEditMode">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="备注">
                  <el-input
                    v-model="assetData.remark"
                    type="textarea"
                    :rows="4"
                    placeholder="请输入备注信息"
                    maxlength="500"
                    show-word-limit
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="制单人">
                  <el-input :value="getCreatorName()" readonly />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="制单时间">
                  <el-input :value="formatDateTime(assetData.createdAt)" readonly />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  ArrowLeft, Edit, Switch, Money, Delete, Check, Close,
  Box, Key, Document, Clock
} from '@element-plus/icons-vue'

// 组件导入
import AssetFormHeader from '../components/AssetFormHeader.vue'
import AssetProductDetail from '../components/AssetProductDetail.vue'
import AssetActivationInfo from '../components/AssetActivationInfo.vue'
import AssetRelatedOrders from '../components/AssetRelatedOrders.vue'
import AssetChangeRecords from '../components/AssetChangeRecords.vue'

// 工具函数
import { formatDateTime } from '@/utils/format.js'

// Composables
import { useAssetData } from '../composables/useAssetData.js'
import { useAuth } from '@/store/auth.js'

// API
import { getEnterprises } from '@/api/enterprise.js'
import { getUsers } from '@/api/user.js'
import { getProducts } from '@/api/product.js'

// 路由
const route = useRoute()
const router = useRouter()

// 数据管理
const {
  loading,
  saving,
  assetData,
  fetchAsset,
  createNewAsset,
  updateExistingAsset,
  deleteExistingAsset,
  initializeNewAsset,
  resetData
} = useAssetData()

// 认证状态
const { state: authState } = useAuth()

// 状态
const activeTab = ref('product')
const isEditMode = ref(false)
const mode = ref('view') // 页面模式：view, edit, create

// 表单引用
const headerFormRef = ref(null)
const productFormRef = ref(null)
const activationFormRef = ref(null)

// 选项数据
const enterpriseOptions = ref([])
const userOptions = ref([])
const productOptions = ref([])

// 计算属性
const assetId = computed(() => route.params.id || null)
const isCreateMode = computed(() => route.query.mode === 'add')
const pageTitle = computed(() => {
  if (isCreateMode.value) return '新增资产'
  if (isEditMode.value) return '修改资产'
  if (assetData.value) {
    return `资产详情 - ${assetData.value.asset_id || ''}`
  }
  return '资产详情'
})

// 加载选项数据
const loadOptions = async () => {
  try {
    const [enterprises, users, products] = await Promise.all([
      getEnterprises(),
      getUsers(), // 一次性加载所有用户
      getProducts()
    ])

    enterpriseOptions.value = enterprises
    userOptions.value = users // 存储所有用户数据
    productOptions.value = products
  } catch (error) {
    ElMessage.error('加载选项数据失败')
  }
}

// 加载资产数据
const loadAssetData = async () => {
  if (assetId.value) {
    await fetchAsset(assetId.value)
  }
}

// 初始化页面
const initializePage = async () => {
  await loadOptions()

  if (isCreateMode.value) {
    mode.value = 'create'
    await initializeNewAsset()
  } else if (assetId.value) {
    mode.value = 'view'
    await loadAssetData()
  }
}

// 工具函数
const getCreatorName = () => {
  if (mode.value === 'create') {
    // 新增模式：显示当前登录用户
    return authState.user?.name || ''
  } else {
    // 查看/编辑模式：显示资产的创建人
    return assetData.value?.creator?.name || ''
  }
}

// 事件处理
const handleEdit = () => {
  mode.value = 'edit'
  isEditMode.value = true
}

const handleCancel = () => {
  if (mode.value === 'create') {
    router.push({ name: 'asset-list' })
  } else {
    resetData()
    mode.value = 'view'
    isEditMode.value = false
    ElMessage.info('已取消操作')
  }
}

const handleSave = async () => {
  try {
    // 表单验证
    await headerFormRef.value?.validate()
    await productFormRef.value?.validate()
    await activationFormRef.value?.validate()

    if (mode.value === 'create') {
      const newAsset = await createNewAsset(assetData.value)
      // 跳转到详情页
      router.replace({ name: 'asset-detail', params: { id: newAsset.id } })
      mode.value = 'view'
      isEditMode.value = false
    } else {
      await updateExistingAsset(assetId.value, assetData.value)
      mode.value = 'view'
      isEditMode.value = false
    }
  } catch (error) {
    // 错误已在 composable 中处理
  }
}

const handleDelete = async () => {
  if (!assetId.value) return

  try {
    await ElMessageBox.confirm(
      `确定要删除资产 [${assetData.value.asset_id}] 吗？`,
      '警告',
      { type: 'warning' }
    )

    await deleteExistingAsset(assetId.value)
    router.push({ name: 'asset-list' })
  } catch (error) {
    if (error !== 'cancel') {
      // 错误已在 composable 中处理
    }
  }
}

const handleRenewal = () => {
  // 跳转到新增变更订单页面，并传递资产信息
  router.push({
    name: 'order-create',
    query: {
      type: 'renewal',
      assetId: assetId.value,
      enterpriseId: assetData.value?.enterprise_id
    }
  })
}

const handleProductChange = (productId) => {
  // 产品变更逻辑
  console.log('产品变更:', productId)
}

// 处理企业选择变化
const handleEnterpriseChange = (enterpriseId) => {
  console.log('企业变更:', enterpriseId)

  // 清空当前选择的用户，因为用户选项会通过计算属性自动更新
  if (assetData.value) {
    assetData.value.user_id = null
  }

  // 如果选择了企业且该企业有关联用户，自动选择该用户
  if (enterpriseId) {
    const selectedEnterprise = enterpriseOptions.value.find(
      enterprise => enterprise.id === enterpriseId
    )
    if (selectedEnterprise?.user_id && assetData.value) {
      assetData.value.user_id = selectedEnterprise.user_id
    }
  }
}

// 返回上一页
const handleBack = () => {
  router.back()
}

// 变更资产
const handleChange = () => {
  router.push({ name: 'asset-change-create', params: { id: assetId.value } })
}

// 用户变更处理
const handleUserChange = (userId) => {
  if (assetData.value) {
    assetData.value.user_id = userId
  }
}

// 数据变更处理
const handleDataChange = (data) => {
  if (assetData.value) {
    Object.assign(assetData.value, data)
  }
}

// 变更记录回滚成功处理
const handleLogReverted = () => {
  // 变更记录回滚后，重新加载资产数据
  loadAssetData()
}

const handleViewChangeDetail = (changeLog) => {
  router.push({
    name: 'asset-changelog-detail',
    params: { changeId: changeLog.id }
  })
}

const handleOrderLinked = (orderId) => {
  if (assetData.value) {
    assetData.value.order_id = orderId
    ElMessage.success('订单已成功关联')
  }
}

// 生命周期
onMounted(() => {
  initializePage()
})
</script>

<style scoped>
.asset-detail-page {
  padding: 24px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

/* 页面头部 */
.page-header {
  margin-bottom: 24px;
  padding: 20px 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
  color: white;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.back-button {
  border: 1px solid rgba(255, 255, 255, 0.3);
  background: rgba(255, 255, 255, 0.1);
  color: white;
  backdrop-filter: blur(10px);
}

.back-button:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.5);
}

.page-title {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: white;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.header-actions .el-button {
  border: 1px solid rgba(255, 255, 255, 0.3);
  background: rgba(255, 255, 255, 0.1);
  color: white;
  backdrop-filter: blur(10px);
}

.header-actions .el-button:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.5);
}

.header-actions .el-button--primary {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
}

/* 主要内容 */
.asset-detail-content {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* 标签页 */
.tabs-section {
  margin: 20px 0;
}

.detail-tabs {
  background: white;
}

.detail-tabs :deep(.el-tabs__content) {
  padding-top: 20px;
}

.detail-tabs :deep(.el-tabs__header) {
  margin-bottom: 0;
}

.detail-tabs :deep(.el-tabs__nav-wrap::after) {
  height: 1px;
}

.tab-label {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 卡片样式 */
.header-card, .footer-card {
  border: none;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.header-card :deep(.el-card__body) {
  padding: 20px;
}

.footer-card :deep(.el-card__body) {
  padding: 20px;
}

.card-header {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .asset-detail-page {
    padding: 16px;
  }

  .page-header {
    padding: 16px;
  }

  .header-content {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }

  .page-title {
    font-size: 20px;
  }
}
</style>
