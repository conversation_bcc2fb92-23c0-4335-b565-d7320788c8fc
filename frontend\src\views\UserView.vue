<script setup>
import { ref, computed, watch } from 'vue';
import CrudPage from '@/components/common/CrudPage.vue';
import { getUsers, createUser, updateUser, deleteUser, getNextUserId, getNextPartnerId } from '@/api/user.js';
import { ElMessage, ElMessageBox } from 'element-plus';
import UserAuthenticationDialog from '@/components/UserAuthenticationDialog.vue';
import { formatDateTime } from '@/utils/format.js';

// 1. 定义表格的列
const columns = ref([
  { prop: 'id', label: 'ID', width: 80 },
  { prop: 'user_id', label: '用户ID', width: 160 },
  { prop: 'name', label: '姓名', width: 120 },
  { prop: 'nickname', label: '昵称', width: 120 },
  { prop: 'mobile', label: '手机号', width: 150 },
  { prop: 'email', label: '邮箱', width: 180, showOverflowTooltip: true },
  { prop: 'is_partner', label: '合伙人', width: 100, isSlot: true }, // 标记为使用插槽
  { prop: 'partner_id', label: '合伙人ID', width: 150 },
  { prop: 'createdAt', label: '创建时间', width: 180, isSlot: true }, // 标记为使用插槽
  { prop: 'updatedAt', label: '修改时间', width: 180, isSlot: true }, // 标记为使用插槽
  { prop: 'remark', label: '备注', minWidth: 200, showOverflowTooltip: true },
]);

// 2. 定义API函数
const api = {
  list: getUsers,
  create: createUser,
  update: updateUser,
  delete: deleteUser,
};

// 3. 状态管理
const dialogVisible = ref(false);
const isEditMode = ref(false); // true: 编辑, false: 新增/复制
const crudPageRef = ref(null);
const selectedItems = ref([]);

// 3.1 认证弹窗专用状态
const authDialogVisible = ref(false);
const currentUserId = ref(null);

// 3.2 表单数据模型及特殊逻辑 (完全保留)
const getInitialForm = () => ({
  user_id: '',
  name: '',
  nickname: '',
  mobile: '',
  email: '',
  password: '',
  is_partner: false,
  partner_id: null,
  commission_base: null,
  commission_extra: null,
  remark: ''
});
const form = ref(getInitialForm());

watch(() => form.value.is_partner, async (isPartner, wasPartner) => {
  if (isPartner) {
    if(form.value.commission_base === null) form.value.commission_base = 0.10;
    if(form.value.commission_extra === null) form.value.commission_extra = 0.00;
    if (!wasPartner) {
      try {
        const res = await getNextPartnerId();
        form.value.partner_id = res.next_id;
      } catch (error) {
        ElMessage.error('获取新合伙人ID失败，请手动输入。');
      }
    }
  } else {
    form.value.partner_id = null;
    form.value.commission_base = null;
    form.value.commission_extra = null;
  }
});


// 4. 计算属性，控制按钮禁用状态
const isEditDisabled = computed(() => selectedItems.value.length !== 1);
const isCopyDisabled = computed(() => selectedItems.value.length !== 1);
const isDeleteDisabled = computed(() => selectedItems.value.length === 0);


// 5. 方法
const handleSelectionChange = (selection) => {
  selectedItems.value = selection;
};

const handleOpenDialog = async (editMode = false, copyMode = false, rowData = null) => {
  isEditMode.value = editMode;
  if (editMode && rowData) {
    form.value = {
      ...rowData,
      password: '',
      commission_base: rowData.commission_base === null ? null : Number(rowData.commission_base),
      commission_extra: rowData.commission_extra === null ? null : Number(rowData.commission_extra)
    };
  } else if (copyMode && rowData) {
    const copiedData = { ...rowData, password: '' };
    delete copiedData.id;
    try {
      const res = await getNextUserId();
      copiedData.user_id = res.next_id;
      if (copiedData.is_partner) {
        const partnerRes = await getNextPartnerId();
        copiedData.partner_id = partnerRes.next_id;
      }
    } catch (error) {
      ElMessage.error('获取新ID失败，请为复制的记录输入唯一的ID。');
      copiedData.user_id = '';
    }
    form.value = copiedData;
    isEditMode.value = false;
  } else {
    form.value = getInitialForm();
    try {
      const res = await getNextUserId();
      form.value.user_id = res.next_id;
    } catch (error) {
      ElMessage.error('获取新用户ID失败，请输入一个唯一的ID。');
    }
  }
  dialogVisible.value = true;
};

const handleDelete = async (ids) => {
  await ElMessageBox.confirm(`确定删除选中的 ${ids.length} 位用户吗？`, '警告', { type: 'warning' });
  try {
    await Promise.all(ids.map(id => api.delete(id)));
    ElMessage.success('删除成功');
    crudPageRef.value?.loadData();
    selectedItems.value = [];
  } catch (error) {
    ElMessage.error('删除失败');
  }
};

const handleBatchDelete = () => {
  if (isDeleteDisabled.value) return;
  const idsToDelete = selectedItems.value.map(item => item.id);
  handleDelete(idsToDelete);
};

// 提交表单 (完全保留)
const handleSubmit = async () => {
  try {
    const dataToSend = { ...form.value };
    if (!dataToSend.password) delete dataToSend.password;

    if (isEditMode.value) {
      await api.update(form.value.id, dataToSend);
      ElMessage.success('更新成功');
    } else {
      if (!form.value.password) {
        ElMessage.error('新增用户时必须设置密码');
        return;
      }
      await api.create(dataToSend);
      ElMessage.success('创建成功');
    }
    dialogVisible.value = false;
    crudPageRef.value?.loadData();
  } catch (error) {
    ElMessage.error((isEditMode.value ? '更新' : '创建') + '失败: ' + (error.response?.data?.message || error.message));
  }
};

// 打开认证弹窗 (保留)
const handleAuth = (row) => {
  currentUserId.value = row.id;
  authDialogVisible.value = true;
};

</script>

<template>
  <CrudPage
    ref="crudPageRef"
    title="用户"
    :columns="columns"
    :api-list="api.list"
    :api-create="api.create"
    :api-update="api.update"
    :api-delete="api.delete"
    :hide-row-actions="false"
    :row-actions-width="100"
    @selection-change="handleSelectionChange"
  >
    <!-- 1. 自定义顶部工具栏 -->
    <template #actions>
      <el-button type="primary" @click="handleOpenDialog(false, false, null)">新增用户</el-button>
       <el-tooltip content="请选择一位用户进行修改" :disabled="!isEditDisabled" placement="top">
        <div style="display: inline-block; margin: 0 6px;"><el-button type="default" :disabled="isEditDisabled" @click="handleOpenDialog(true, false, selectedItems[0])">修改</el-button></div>
      </el-tooltip>
      <el-tooltip content="请选择一位用户进行复制" :disabled="!isCopyDisabled" placement="top">
        <div style="display: inline-block; margin: 0 6px;"><el-button type="success" :disabled="isCopyDisabled" @click="handleOpenDialog(false, true, selectedItems[0])">复制</el-button></div>
      </el-tooltip>
      <el-tooltip content="请至少选择一位用户进行删除" :disabled="!isDeleteDisabled" placement="top">
        <div style="display: inline-block; margin: 0 6px;"><el-button type="danger" :disabled="isDeleteDisabled" @click="handleBatchDelete">删除</el-button></div>
      </el-tooltip>
    </template>
    
    <!-- 2. 自定义表格列的显示 -->
    <template #col-is_partner="{ row }">
      <el-tag :type="row.is_partner ? 'success' : 'info'">{{ row.is_partner ? '是' : '否' }}</el-tag>
    </template>
    <template #col-createdAt="{ row }">
      {{ formatDateTime(row.createdAt) }}
    </template>
     <template #col-updatedAt="{ row }">
      {{ formatDateTime(row.updatedAt) }}
    </template>

    <!-- 3. 自定义行内操作按钮 -->
    <template #row-actions="{ row }">
      <el-button size="small" type="success" @click="handleAuth(row)">认证</el-button>
    </template>

    <!-- 4. 自定义弹窗实现 (大部分沿用旧代码) -->
    <template #dialog>
      <el-dialog v-model="dialogVisible" :title="isEditMode ? '编辑用户' : '新增用户'" width="600px" :close-on-click-modal="false">
        <el-form :model="form" label-width="100px">
          <el-form-item label="用户ID"><el-input v-model="form.user_id"></el-input></el-form-item>
          <el-form-item label="合伙人ID" v-if="form.is_partner"><el-input v-model="form.partner_id"></el-input></el-form-item>
          <el-form-item label="姓名"><el-input v-model="form.name"></el-input></el-form-item>
          <el-form-item label="昵称"><el-input v-model="form.nickname"></el-input></el-form-item>
          <el-form-item label="手机号"><el-input v-model="form.mobile"></el-input></el-form-item>
          <el-form-item label="邮箱"><el-input v-model="form.email"></el-input></el-form-item>
          <el-form-item label="密码"><el-input v-model="form.password" type="password" show-password :placeholder="isEditMode ? '留空则不修改密码' : '请输入密码'"></el-input></el-form-item>
          <el-form-item label="是否合伙人"><el-switch v-model="form.is_partner" /></el-form-item>
          <el-form-item label="基础分润" v-if="form.is_partner"><el-input-number v-model="form.commission_base" :precision="4" :step="0.01" :min="0" :max="1"></el-input-number></el-form-item>
          <el-form-item label="额外分润" v-if="form.is_partner"><el-input-number v-model="form.commission_extra" :precision="4" :step="0.01" :min="0" :max="1"></el-input-number></el-form-item>
          <el-form-item label="备注"><el-input v-model="form.remark" type="textarea"></el-input></el-form-item>
        </el-form>
        <template #footer>
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit">提交</el-button>
        </template>
      </el-dialog>
    </template>
  </CrudPage>

  <!-- 认证弹窗 (完全保留) -->
  <UserAuthenticationDialog 
    v-if="authDialogVisible"
    v-model="authDialogVisible" 
    :user-id="currentUserId"
    @success="crudPageRef?.loadData()"
  />
</template>

<style scoped>
/* 可选：如果 CrudPage 中已有 padding，这里的可以移除或调整 */
.page-container {
  padding: 20px;
}
.action-bar {
  margin-bottom: 20px;
}
</style> 