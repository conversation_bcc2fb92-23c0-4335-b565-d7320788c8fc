import { createRouter, createWebHistory } from 'vue-router'
import EnterpriseView from '../views/EnterpriseView.vue'
import LoginView from '../views/LoginView.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/login',
      name: 'login',
      component: LoginView,
      meta: { requiresAuth: false }
    },
    {
      path: '/',
      redirect: '/enterprises',
      meta: { requiresAuth: true }
    },
    {
      path: '/enterprises',
      name: 'enterprises',
      component: EnterpriseView,
    },
    {
      path: '/products',
      name: 'products',
      component: () => import('../views/ProductView.vue')
    },
    {
      path: '/features',
      name: 'features',
      component: () => import('../views/FeatureView.vue')
    },
    {
      path: '/assets',
      name: 'asset-management',
      meta: { title: '资产管理', icon: 'Box' },
      redirect: '/assets/list', // 默认重定向到列表页
      children: [
        {
          path: 'list',
          name: 'asset-list',
          component: () => import('@/views/Asset/pages/AssetList.vue'),
          meta: { title: '资产列表' }
        },
        {
          path: 'form/:id?',
          name: 'asset-form',
          component: () => import('@/views/Asset/pages/AssetForm.vue'),
          meta: { title: '资产表单' }
        },
        {
          path: 'detail/:id',
          name: 'asset-detail',
          component: () => import('@/views/Asset/pages/AssetDetail.vue'),
          meta: { title: '资产详情' }
        },
        {
          path: 'change/create/:id',
          name: 'asset-change-create',
          component: () => import('@/views/Asset/pages/AssetChangeCreate.vue'),
          meta: { title: '创建变更' }
        },
        {
          path: 'change/detail/:id',
          name: 'asset-change-detail',
          component: () => import('@/views/Asset/pages/AssetChangeDetail.vue'),
          meta: { title: '变更详情' }
        },
        {
          path: 'change/list',
          name: 'asset-change-list',
          component: () => import('@/views/Asset/pages/AssetChangeList.vue'),
          meta: { title: '变更列表' }
        }
      ]
    },
    {
      path: '/orders',
      name: 'order-management',
      meta: { title: '订单管理', icon: 'Document' },
      redirect: '/orders/review', // 默认重定向到订单审核页面
      children: [
        {
          path: 'review',
          name: 'OrderReviewList',
          component: () => import('@/views/order/pages/OrderReviewList.vue'),
          meta: { title: '订单审核' }
        },
        {
          path: 'product',
          name: 'ProductOrderList',
          component: () => import('@/views/order/pages/ProductOrderList.vue'),
          meta: { title: '产品订单' }
        },
        {
          path: 'product/form',
          name: 'ProductOrderCreate',
          component: () => import('@/views/order/pages/ProductOrderForm.vue'),
          meta: { title: '新增产品订单' }
        },
        {
          path: 'product/form/:id',
          name: 'ProductOrderForm',
          component: () => import('@/views/order/pages/ProductOrderForm.vue'),
          meta: { title: '产品订单表单' }
        },
        {
          path: 'service',
          name: 'ServiceOrderList',
          component: () => import('@/views/order/pages/ServiceOrderList.vue'),
          meta: { title: '服务订单' }
        },
        {
          path: 'service/form',
          name: 'ServiceOrderCreate',
          component: () => import('@/views/order/pages/ServiceOrderForm.vue'),
          meta: { title: '新增服务订单' }
        },
        {
          path: 'service/form/:id',
          name: 'ServiceOrderForm',
          component: () => import('@/views/order/pages/ServiceOrderForm.vue'),
          meta: { title: '服务订单表单' }
        }
      ]
    },
    {
      path: '/employees',
      name: 'employees',
      // route level code-splitting
      // this generates a separate chunk (About.[hash].js) for this route
      // which is lazy-loaded when the route is visited.
      component: () => import('../views/EmployeeView.vue'),
    },
    {
      path: '/users',
      name: 'user-management',
      component: () => import('../views/UserView.vue')
    },

  ],
})

//检查所有页面的访问权限，自动将未登录的用户引导至登录页。
router.beforeEach((to, from, next) => {
  const token = localStorage.getItem('authToken')

  const requiresAuth = to.matched.some(record => record.meta.requiresAuth !== false)

  if (requiresAuth && !token) {
    next({ name: 'login' })
  } else if (to.name === 'login' && token) {
    next({ path: '/' })
  } else {
    next()
  }
})

export default router
