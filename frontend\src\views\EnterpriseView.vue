<script setup>
import { ref, onMounted, computed } from 'vue';
import CrudPage from '@/components/common/CrudPage.vue';
import { getEnterprises, createEnterprise, updateEnterprise, deleteEnterprise, getNextEnterpriseId } from '@/api/enterprise.js';
import { getEmployees } from '@/api/employee.js';
import { getUsers } from '@/api/user.js';
import { useAuth } from '@/store/auth.js';
import { ElMessage, ElMessageBox } from 'element-plus';
import EnterpriseFollowupDialog from '@/components/Enterprise/EnterpriseFollowupDialog.vue';
import { formatDateTime } from '@/utils/format.js';
import service from '@/utils/request_extra.js'; // [新增] 导入axios实例

// 1. CrudPage所需配置
const crudPageRef = ref(null);
const api = {
  list: getEnterprises,
  create: createEnterprise,
  update: updateEnterprise,
  delete: deleteEnterprise,
};
const columns = ref([
  { prop: 'id', label: '自增ID', width: 80 },
  { prop: 'enterprise_id', label: '企业ID', width: 120, sortable: true },
  { prop: 'name', label: '企业名称', width: 250, showOverflowTooltip: true, sortable: true, isSlot: true },
  { prop: 'invoice_info', label: '开票信息', width: 200, isSlot: true },
  { prop: 'license_image', label: '营业执照', width: 100, isSlot: true }, // 新增营业执照列
  { prop: 'address', label: '地址', width: 220, showOverflowTooltip: true },
  { prop: 'contact_person', label: '联系人', width: 120 },
  { prop: 'contact_phone', label: '联系电话', width: 150 },
  { prop: 'employee.name', label: '负责人', width: 120 },
  { prop: 'user.name', label: '关联用户', width: 120 },
  { prop: 'createdAt', label: '创建时间', width: 180, isSlot: true },
  { prop: 'remark', label: '备注', minWidth: 200, showOverflowTooltip: true },
]);

// 2. 页面和弹窗状态
const { state: authState } = useAuth();
const isAdmin = computed(() => authState.user?.role === 'admin');
const selectedItems = ref([]);
const employeesList = ref([]);
const usersList = ref([]);
const dialogVisible = ref(false);
const dialogMode = ref('create'); // 'create' | 'edit'
const formRef = ref(null);
const followupDialogVisible = ref(false);
const currentEnterprise = ref(null);
const isSubmitting = ref(false);
const uploadRef = ref(null);
const licenseFile = ref(null);

const getInitialForm = () => ({
  enterprise_id: '', name: '', tax_number: '', bank_name: '', bank_account: '',
  invoice_type: '普票', address: '', contact_person: '', contact_phone: '',
  employee_id: null, user_id: null, remark: '', license_image: ''
});
const form = ref(getInitialForm());

// 3. 计算属性 (用于控制按钮禁用状态)
const isEditDisabled = computed(() => selectedItems.value.length !== 1);
const isCopyDisabled = computed(() => selectedItems.value.length !== 1); // [+] 复制按钮禁用状态
const isFollowupDisabled = computed(() => selectedItems.value.length !== 1);
const isDeleteDisabled = computed(() => selectedItems.value.length === 0);
const ownerNameForDisplay = computed(() => {
    if (isAdmin.value || !form.value.employee_id) return '';
    const owner = employeesList.value.find(emp => emp.id === form.value.employee_id);
    return owner ? owner.name : '未知员工';
});

// 4. 方法
const loadDependencies = async () => {
  try {
    const [employeesRes, usersRes] = await Promise.all([getEmployees(), getUsers()]);
    employeesList.value = employeesRes;
    usersList.value = usersRes;
  } catch (error) {
    ElMessage.error('获取员工或用户列表失败');
  }
};

const handleSelectionChange = (selection) => {
  selectedItems.value = selection;
};

const openDialog = async (mode, data = null) => {
  dialogMode.value = mode;
  licenseFile.value = null;
  uploadRef.value?.clearFiles();

  if (mode === 'create' || mode === 'copy') {
    form.value = data || getInitialForm();
    if (mode === 'create' && !isAdmin.value) {
      form.value.employee_id = authState.user.id;
    }
    // For both create and copy, we get a new ID.
    (async () => {
      try {
        const res = await getNextEnterpriseId();
        form.value.enterprise_id = res.next_id;
        if (mode === 'copy') {
          // If copying, we need to ensure original data is preserved but with new ID
          const originalData = { ...data };
          delete originalData.id;
          delete originalData.createdAt;
          delete originalData.updatedAt;
          form.value = { ...originalData, enterprise_id: res.next_id };
        }
      } catch (error) {
        ElMessage.error('获取新企业ID失败，请手动输入一个唯一的ID。');
      }
    })();
  } else { // 'edit' mode
    form.value = { ...data, employee_id: data.employee?.id, user_id: data.user?.id };
  }
  dialogVisible.value = true;
};

// [+] 新增复制处理方法
const handleCopy = async () => {
    if(isCopyDisabled.value) return;
    const original = selectedItems.value[0];
    openDialog('copy', original);
};

const handleBatchDelete = () => {
    crudPageRef.value?.batchDelete(selectedItems.value, 'name');
};

const handleSubmit = async () => {
  await formRef.value?.validate();
  isSubmitting.value = true;
  try {
    if (!isAdmin.value) {
      form.value.employee_id = authState.user.id;
    }

    const formData = new FormData();
    for (const key in form.value) {
      if (form.value[key] !== null && form.value[key] !== undefined) {
        formData.append(key, form.value[key]);
      }
    }
    if (licenseFile.value) {
      formData.append('license_image', licenseFile.value);
    }
    
    if (dialogMode.value === 'edit') {
      await api.update(form.value.id, formData);
      ElMessage.success('更新成功');
    } else {
      await api.create(formData);
      ElMessage.success('创建成功');
    }
    dialogVisible.value = false;
    crudPageRef.value?.loadData();
  } catch (error) {
    ElMessage.error(`操作失败: ${error.response?.data?.message || error.message}`);
  } finally {
    isSubmitting.value = false;
  }
};

const handleOpenFollowups = () => {
  if (isFollowupDisabled.value) return;
  currentEnterprise.value = selectedItems.value[0];
  followupDialogVisible.value = true;
};

const handleLicenseFileChange = (file) => {
  licenseFile.value = file.raw;
};

// [新增] 处理营业执照下载的方法
const handleDownloadLicense = (row) => {
  if (!row.id || !row.license_image) {
    ElMessage.warning('没有可供下载的营业执照');
    return;
  }
  const baseURL = service.defaults.baseURL || '';
  const token = localStorage.getItem('authToken');
  const downloadUrl = `${baseURL}/enterprises/${row.id}/license/download?token=${token}`;
  window.open(downloadUrl, '_blank');
};

onMounted(() => {
  loadDependencies();
});
</script>

<template>
  <CrudPage
    ref="crudPageRef"
    title="企业"
    :api-list="api.list"
    :api-delete="api.delete"
    :api-create="api.create"
    :api-update="api.update"
    :columns="columns"
    :hide-row-actions="true" 
    @selection-change="handleSelectionChange"
  >
    <!-- 1. 顶部工具栏 -->
    <template #actions>
      <el-button type="primary" @click="openDialog('create')">新增企业</el-button>
      <el-button @click="openDialog('edit', selectedItems[0])" :disabled="isEditDisabled">修改</el-button>
      <el-button type="success" @click="handleCopy" :disabled="isCopyDisabled">复制</el-button>
      <el-button @click="handleOpenFollowups" :disabled="isFollowupDisabled">跟进</el-button>
      <el-button type="danger" @click="handleBatchDelete" :disabled="isDeleteDisabled">删除</el-button>
    </template>
    
    <!-- 2. 行内操作 (已通过 hide-row-actions 隐藏) -->

    <!-- 3. 自定义列渲染 -->
    <template #col-name="{ row }">
      <el-link type="primary" @click="openDialog('edit', row)">{{ row.name }}</el-link>
    </template>
    <template #col-invoice_info="{ row }">
      <el-popover placement="top-start" title="开票信息详情" :width="250" trigger="hover">
        <template #reference><div style="cursor: pointer;">税号: {{ row.tax_number || 'N/A' }}</div></template>
        <div>
          <p><strong>税号:</strong> {{ row.tax_number || '未提供' }}</p>
          <p><strong>开户行:</strong> {{ row.bank_name || '未提供' }}</p>
          <p><strong>银行账户:</strong> {{ row.bank_account || '未提供' }}</p>
          <p><strong>开票类型:</strong> {{ row.invoice_type || '未提供' }}</p>
        </div>
      </el-popover>
    </template>
    <!-- [+] 新增营业执照列的渲染 -->
    <template #col-license_image="{ row }">
        <el-button
          v-if="row.license_image"
          type="primary"
          link
          @click="handleDownloadLicense(row)"
        >
          下载
        </el-button>
        <span v-else>未上传</span>
    </template>
    <template #col-createdAt="{ row }">{{ formatDateTime(row.createdAt) }}</template>

    <!-- 4. 对话框 (使用#dialog插槽) -->
    <template #dialog>
      <el-dialog 
        :model-value="dialogVisible"
        :title="dialogMode === 'edit' ? '修改企业' : (dialogMode === 'copy' ? '复制企业' : '新增企业')"
        width="60%"
        @close="dialogVisible = false"
      >
        <el-form ref="formRef" :model="form" label-width="120px" :rules="rules">
          <el-form-item label="企业ID" prop="enterprise_id">
            <el-input v-model="form.enterprise_id" placeholder="请输入企业ID"></el-input>
          </el-form-item>
          <el-form-item label="企业名称" prop="name">
            <el-input v-model="form.name" placeholder="请输入企业名称"></el-input>
          </el-form-item>
          <el-form-item label="企业税号" prop="tax_number">
            <el-input v-model="form.tax_number"></el-input>
          </el-form-item>
          <el-form-item label="开户行" prop="bank_name">
            <el-input v-model="form.bank_name"></el-input>
          </el-form-item>
          <el-form-item label="银行账户" prop="bank_account">
            <el-input v-model="form.bank_account"></el-input>
          </el-form-item>
          <el-form-item label="开票类型" prop="invoice_type">
            <el-select v-model="form.invoice_type" placeholder="请选择开票类型">
              <el-option label="普票" value="普票"></el-option>
              <el-option label="专票" value="专票"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="联系人" prop="contact_person">
            <el-input v-model="form.contact_person"></el-input>
          </el-form-item>
          <el-form-item label="联系电话" prop="contact_phone">
            <el-input v-model="form.contact_phone"></el-input>
          </el-form-item>
          <el-form-item label="地址" prop="address">
            <el-input v-model="form.address"></el-input>
          </el-form-item>
          <el-form-item label="负责人" prop="employee_id">
            <el-select v-if="isAdmin" v-model="form.employee_id" placeholder="请选择负责人" filterable clearable>
              <el-option v-for="item in employeesList" :key="item.id" :label="item.name" :value="item.id"></el-option>
            </el-select>
            <el-input v-else :value="ownerNameForDisplay" disabled />
          </el-form-item>
          <el-form-item label="关联用户" prop="user_id">
            <el-select v-model="form.user_id" placeholder="请选择关联用户" filterable clearable>
              <el-option v-for="item in usersList" :key="item.id" :label="item.name" :value="item.id"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="备注" prop="remark">
            <el-input v-model="form.remark" type="textarea"></el-input>
          </el-form-item>
          <el-form-item label="营业执照" prop="license_image">
            <el-upload
              ref="uploadRef"
              action="#" 
              :auto-upload="false"
              :on-change="handleLicenseFileChange"
              :limit="1"
            >
              <el-button type="primary">点击上传</el-button>
              <template #tip>
                <div class="el-upload__tip">
                  <p v-if="form.license_image && !licenseFile">
                    当前文件: {{ form.license_image.split('/').pop() }}
                  </p>
                  只允许上传一张图片，新文件将覆盖旧文件。
                </div>
              </template>
            </el-upload>
          </el-form-item>
        </el-form>
        <template #footer>
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="isSubmitting">确定</el-button>
        </template>
      </el-dialog>
    </template>
  </CrudPage>

  <!-- 跟进记录弹窗 (保持独立) -->
  <EnterpriseFollowupDialog
    v-if="followupDialogVisible"
    v-model:visible="followupDialogVisible"
    :enterprise="currentEnterprise"
  />
</template>

<style scoped>
/* 可以保留或删除原有样式 */
</style> 