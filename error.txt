监听到价格字段变化: Object
ProductOrderForm-m6BI_tS5.js:1 ProductOrderForm orderData 企业/用户ID变化: Object
ProductOrderForm-m6BI_tS5.js:1 ProductOrderForm 组件挂载，当前模式: create 是否编辑: true
ProductOrderForm-m6BI_tS5.js:1 初始化新产品订单
OrderStatusBadge-B9tdyrdr.js:1 订单编码生成成功: PO20250730ESR
OrderStatusBadge-B9tdyrdr.js:1 OrderHeader updateParent 被调用，当前数据: Object
ProductOrderForm-m6BI_tS5.js:1 表头数据更新: Object
ProductOrderForm-m6BI_tS5.js:1 监听到产品信息变化: Proxy(Object)
ProductOrderForm-m6BI_tS5.js:1 更新后的订单金额: Object
ProductOrderForm-m6BI_tS5.js:1 监听到价格字段变化: Object
ProductOrderForm-m6BI_tS5.js:1 ProductOrderForm orderData 企业/用户ID变化: Object
ServiceOrderForm-B30GcQVv.js:1 ServiceInfo组件接收到的数据: Proxy(Object)
ServiceOrderForm-B30GcQVv.js:1 服务明细数据: Proxy(Array)
ServiceOrderForm-B30GcQVv.js:1 ServiceInfo组件接收到的数据: Proxy(Object)
ServiceOrderForm-B30GcQVv.js:1 服务明细数据: Proxy(Array)
ServiceOrderForm-B30GcQVv.js:1 ServiceOrderForm 组件挂载，当前模式: create 是否编辑: true
ServiceOrderForm-B30GcQVv.js:1 初始化新服务订单
OrderStatusBadge-B9tdyrdr.js:1 订单编码生成成功: SO202507309VD
OrderStatusBadge-B9tdyrdr.js:1 OrderHeader updateParent 被调用，当前数据: Object
ServiceOrderForm-B30GcQVv.js:1 表头数据更新: Object
ServiceOrderForm-B30GcQVv.js:1 ServiceInfo组件接收到的数据: Proxy(Object)
ServiceOrderForm-B30GcQVv.js:1 服务明细数据: Proxy(Array)
OrderStatusBadge-B9tdyrdr.js:1 企业变化处理: Object
OrderStatusBadge-B9tdyrdr.js:1 OrderHeader updateParent 被调用，当前数据: Object
ServiceOrderForm-B30GcQVv.js:1 表头数据更新: Object
ServiceOrderForm-B30GcQVv.js:1 ServiceInfo组件接收到的数据: Proxy(Object)
ServiceOrderForm-B30GcQVv.js:1 服务明细数据: Proxy(Array)
OrderStatusBadge-B9tdyrdr.js:1 企业字段验证: Object
OrderStatusBadge-B9tdyrdr.js:1 用户字段验证: Object
OrderStatusBadge-B9tdyrdr.js:1 企业字段验证: Object
OrderStatusBadge-B9tdyrdr.js:1 OrderHeader updateParent 被调用，当前数据: Object
ServiceOrderForm-B30GcQVv.js:1 表头数据更新: Object
ServiceOrderForm-B30GcQVv.js:1 ServiceInfo组件接收到的数据: Proxy(Object)
ServiceOrderForm-B30GcQVv.js:1 服务明细数据: Proxy(Array)
OrderStatusBadge-B9tdyrdr.js:1 企业字段验证: Object
OrderStatusBadge-B9tdyrdr.js:1 OrderHeader updateParent 被调用，当前数据: Object
ServiceOrderForm-B30GcQVv.js:1 表头数据更新: Object
ServiceOrderForm-B30GcQVv.js:1 ServiceInfo组件接收到的数据: Proxy(Object)
ServiceOrderForm-B30GcQVv.js:1 服务明细数据: Proxy(Array)
OrderStatusBadge-B9tdyrdr.js:1 加载合伙人信息，ID: 15
OrderStatusBadge-B9tdyrdr.js:1 合伙人已在列表中: 张小草
OrderStatusBadge-B9tdyrdr.js:1 OrderHeader updateParent 被调用，当前数据: Object
ServiceOrderForm-B30GcQVv.js:1 表头数据更新: Object
OrderStatusBadge-B9tdyrdr.js:1 加载合伙人信息，ID: 15
OrderStatusBadge-B9tdyrdr.js:1 合伙人已在列表中: 张小草
ServiceOrderForm-B30GcQVv.js:1 ServiceInfo组件接收到的数据: Proxy(Object)
ServiceOrderForm-B30GcQVv.js:1 服务明细数据: Proxy(Array)
OrderStatusBadge-B9tdyrdr.js:1 OrderHeader updateParent 被调用，当前数据: Object
ServiceOrderForm-B30GcQVv.js:1 表头数据更新: Object
ServiceOrderForm-B30GcQVv.js:1 ServiceInfo组件接收到的数据: Proxy(Object)
ServiceOrderForm-B30GcQVv.js:1 服务明细数据: Proxy(Array)
OrderStatusBadge-B9tdyrdr.js:1 OrderHeader updateParent 被调用，当前数据: Object
ServiceOrderForm-B30GcQVv.js:1 表头数据更新: Object
OrderStatusBadge-B9tdyrdr.js:1 OrderHeader updateParent 被调用，当前数据: Object
ServiceOrderForm-B30GcQVv.js:1 表头数据更新: Object
ServiceOrderForm-B30GcQVv.js:1 ServiceInfo组件接收到的数据: Proxy(Object)
ServiceOrderForm-B30GcQVv.js:1 服务明细数据: Proxy(Array)
ServiceOrderForm-B30GcQVv.js:1 ServiceInfo组件接收到的数据: Proxy(Object)
ServiceOrderForm-B30GcQVv.js:1 服务明细数据: Proxy(Array)
OrderStatusBadge-B9tdyrdr.js:1 自动计算分润金额: Object
OrderStatusBadge-B9tdyrdr.js:1 OrderHeader updateParent 被调用，当前数据: Object
ServiceOrderForm-B30GcQVv.js:1 表头数据更新: Object
ServiceOrderForm-B30GcQVv.js:1 ServiceInfo组件接收到的数据: Proxy(Object)
ServiceOrderForm-B30GcQVv.js:1 服务明细数据: Proxy(Array)
ServiceOrderForm-B30GcQVv.js:1 ServiceInfo组件接收到的数据: Proxy(Object)
ServiceOrderForm-B30GcQVv.js:1 服务明细数据: Proxy(Array)
OrderStatusBadge-B9tdyrdr.js:1 自动计算分润金额: Object
OrderStatusBadge-B9tdyrdr.js:1 OrderHeader updateParent 被调用，当前数据: Object
ServiceOrderForm-B30GcQVv.js:1 表头数据更新: Object
ServiceOrderForm-B30GcQVv.js:1 ServiceInfo组件接收到的数据: Proxy(Object)
ServiceOrderForm-B30GcQVv.js:1 服务明细数据: Proxy(Array)
ServiceOrderForm-B30GcQVv.js:1 ServiceInfo组件接收到的数据: Proxy(Object)
ServiceOrderForm-B30GcQVv.js:1 服务明细数据: Proxy(Array)
OrderStatusBadge-B9tdyrdr.js:1 自动计算分润金额: Object
OrderStatusBadge-B9tdyrdr.js:1 OrderHeader updateParent 被调用，当前数据: Object
ServiceOrderForm-B30GcQVv.js:1 表头数据更新: Object
ServiceOrderForm-B30GcQVv.js:1 ServiceInfo组件接收到的数据: Proxy(Object)
ServiceOrderForm-B30GcQVv.js:1 服务明细数据: Proxy(Array)
ServiceOrderForm-B30GcQVv.js:1 ServiceInfo组件接收到的数据: Proxy(Object)
ServiceOrderForm-B30GcQVv.js:1 服务明细数据: Proxy(Array)
ServiceOrderForm-B30GcQVv.js:1 ServiceInfo组件接收到的数据: Proxy(Object)
ServiceOrderForm-B30GcQVv.js:1 服务明细数据: Proxy(Array)
ServiceOrderForm-B30GcQVv.js:1 ServiceInfo组件接收到的数据: Proxy(Object)
ServiceOrderForm-B30GcQVv.js:1 服务明细数据: Proxy(Array)
OrderStatusBadge-B9tdyrdr.js:1 自动计算分润金额: Object
OrderStatusBadge-B9tdyrdr.js:1 OrderHeader updateParent 被调用，当前数据: Object
ServiceOrderForm-B30GcQVv.js:1 表头数据更新: Object
ServiceOrderForm-B30GcQVv.js:1 ServiceInfo组件接收到的数据: Proxy(Object)
ServiceOrderForm-B30GcQVv.js:1 服务明细数据: Proxy(Array)
ServiceOrderForm-B30GcQVv.js:1 ServiceInfo组件接收到的数据: Proxy(Object)
ServiceOrderForm-B30GcQVv.js:1 服务明细数据: Proxy(Array)