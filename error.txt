监听到价格字段变化: {standardPrice: 0, actualPrice: 0}
ProductOrderForm-C-g5v4bk.js:1 ProductOrderForm orderData 企业/用户ID变化: {enterprise_id: null, user_id: null, enterprise_type: 'object', user_type: 'object'}
ProductOrderForm-C-g5v4bk.js:1 ProductOrderForm 组件挂载，当前模式: create 是否编辑: true
ProductOrderForm-C-g5v4bk.js:1 初始化新产品订单
OrderStatusBadge-CPr9zilU.js:1 订单编码生成成功: PO202507297HK
OrderStatusBadge-CPr9zilU.js:1 OrderHeader updateParent 被调用，当前数据: {enterprise_id: null, user_id: null, is_partner_order: false, partner_user_id: null, commission_base: null, …}
ProductOrderForm-C-g5v4bk.js:1 表头数据更新: {order_id: 'PO202507297HK', order_category: '产品订单', enterprise_id: null, asset_id: null, user_id: null, …}
ProductOrderForm-C-g5v4bk.js:1 监听到产品信息变化: Proxy(Object) {product_id: null, user_count: 1, account_count: 1, duration_months: 12, selected_features: Array(0), …}
ProductOrderForm-C-g5v4bk.js:1 更新后的订单金额: {standard_amount: 0, actual_amount: 0}
ProductOrderForm-C-g5v4bk.js:1 监听到价格字段变化: {standardPrice: 0, actualPrice: 0}
ProductOrderForm-C-g5v4bk.js:1 ProductOrderForm orderData 企业/用户ID变化: {enterprise_id: null, user_id: null, enterprise_type: 'object', user_type: 'object'}
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/orders/next-id?orderType=product"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-BXGQS6JU.js:20
xhr @ index-BXGQS6JU.js:20
Xg @ index-BXGQS6JU.js:22
Promise.then
_request @ index-BXGQS6JU.js:23
request @ index-BXGQS6JU.js:22
Ga.<computed> @ index-BXGQS6JU.js:23
(匿名) @ index-BXGQS6JU.js:18
c @ order-BLZdhLwT.js:1
ce @ OrderStatusBadge-CPr9zilU.js:1
(匿名) @ OrderStatusBadge-CPr9zilU.js:1
(匿名) @ index-BXGQS6JU.js:14
Yi @ index-BXGQS6JU.js:14
Wo @ index-BXGQS6JU.js:14
t.__weh.t.__weh @ index-BXGQS6JU.js:14
A1 @ index-BXGQS6JU.js:14
L1 @ index-BXGQS6JU.js:14
Promise.then
P1 @ index-BXGQS6JU.js:14
gT @ index-BXGQS6JU.js:14
n$ @ index-BXGQS6JU.js:14
Zv.i.scheduler @ index-BXGQS6JU.js:14
d.scheduler @ index-BXGQS6JU.js:10
trigger @ index-BXGQS6JU.js:10
Bv @ index-BXGQS6JU.js:10
notify @ index-BXGQS6JU.js:10
trigger @ index-BXGQS6JU.js:10
set value @ index-BXGQS6JU.js:10
M @ index-BXGQS6JU.js:63
(匿名) @ index-BXGQS6JU.js:63
Promise.then
O @ index-BXGQS6JU.js:63
w @ index-BXGQS6JU.js:63
install @ index-BXGQS6JU.js:63
use @ index-BXGQS6JU.js:14
(匿名) @ index-BXGQS6JU.js:63
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/products"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-BXGQS6JU.js:20
xhr @ index-BXGQS6JU.js:20
Xg @ index-BXGQS6JU.js:22
Promise.then
_request @ index-BXGQS6JU.js:23
request @ index-BXGQS6JU.js:22
Ga.<computed> @ index-BXGQS6JU.js:23
(匿名) @ index-BXGQS6JU.js:18
$ @ product-elNrP0D8.js:1
V @ ProductOrderForm-C-g5v4bk.js:1
(匿名) @ ProductOrderForm-C-g5v4bk.js:1
(匿名) @ index-BXGQS6JU.js:14
Yi @ index-BXGQS6JU.js:14
Wo @ index-BXGQS6JU.js:14
t.__weh.t.__weh @ index-BXGQS6JU.js:14
A1 @ index-BXGQS6JU.js:14
L1 @ index-BXGQS6JU.js:14
Promise.then
P1 @ index-BXGQS6JU.js:14
gT @ index-BXGQS6JU.js:14
n$ @ index-BXGQS6JU.js:14
Zv.i.scheduler @ index-BXGQS6JU.js:14
d.scheduler @ index-BXGQS6JU.js:10
trigger @ index-BXGQS6JU.js:10
Bv @ index-BXGQS6JU.js:10
notify @ index-BXGQS6JU.js:10
trigger @ index-BXGQS6JU.js:10
set value @ index-BXGQS6JU.js:10
M @ index-BXGQS6JU.js:63
(匿名) @ index-BXGQS6JU.js:63
Promise.then
O @ index-BXGQS6JU.js:63
w @ index-BXGQS6JU.js:63
install @ index-BXGQS6JU.js:63
use @ index-BXGQS6JU.js:14
(匿名) @ index-BXGQS6JU.js:63
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/users"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-BXGQS6JU.js:20
xhr @ index-BXGQS6JU.js:20
Xg @ index-BXGQS6JU.js:22
Promise.then
_request @ index-BXGQS6JU.js:23
request @ index-BXGQS6JU.js:22
Ga.<computed> @ index-BXGQS6JU.js:23
(匿名) @ index-BXGQS6JU.js:18
yte @ index-BXGQS6JU.js:63
O @ OrderStatusBadge-CPr9zilU.js:1
initialize @ OrderStatusBadge-CPr9zilU.js:1
(匿名) @ OrderStatusBadge-CPr9zilU.js:1
await in (匿名)
(匿名) @ index-BXGQS6JU.js:14
Yi @ index-BXGQS6JU.js:14
Wo @ index-BXGQS6JU.js:14
t.__weh.t.__weh @ index-BXGQS6JU.js:14
A1 @ index-BXGQS6JU.js:14
L1 @ index-BXGQS6JU.js:14
Promise.then
P1 @ index-BXGQS6JU.js:14
gT @ index-BXGQS6JU.js:14
n$ @ index-BXGQS6JU.js:14
Zv.i.scheduler @ index-BXGQS6JU.js:14
d.scheduler @ index-BXGQS6JU.js:10
trigger @ index-BXGQS6JU.js:10
Bv @ index-BXGQS6JU.js:10
notify @ index-BXGQS6JU.js:10
trigger @ index-BXGQS6JU.js:10
set value @ index-BXGQS6JU.js:10
M @ index-BXGQS6JU.js:63
(匿名) @ index-BXGQS6JU.js:63
Promise.then
O @ index-BXGQS6JU.js:63
w @ index-BXGQS6JU.js:63
install @ index-BXGQS6JU.js:63
use @ index-BXGQS6JU.js:14
(匿名) @ index-BXGQS6JU.js:63
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/enterprises"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-BXGQS6JU.js:20
xhr @ index-BXGQS6JU.js:20
Xg @ index-BXGQS6JU.js:22
Promise.then
_request @ index-BXGQS6JU.js:23
request @ index-BXGQS6JU.js:22
Ga.<computed> @ index-BXGQS6JU.js:23
(匿名) @ index-BXGQS6JU.js:18
vte @ index-BXGQS6JU.js:63
B @ OrderStatusBadge-CPr9zilU.js:1
initialize @ OrderStatusBadge-CPr9zilU.js:1
(匿名) @ OrderStatusBadge-CPr9zilU.js:1
await in (匿名)
(匿名) @ index-BXGQS6JU.js:14
Yi @ index-BXGQS6JU.js:14
Wo @ index-BXGQS6JU.js:14
t.__weh.t.__weh @ index-BXGQS6JU.js:14
A1 @ index-BXGQS6JU.js:14
L1 @ index-BXGQS6JU.js:14
Promise.then
P1 @ index-BXGQS6JU.js:14
gT @ index-BXGQS6JU.js:14
n$ @ index-BXGQS6JU.js:14
Zv.i.scheduler @ index-BXGQS6JU.js:14
d.scheduler @ index-BXGQS6JU.js:10
trigger @ index-BXGQS6JU.js:10
Bv @ index-BXGQS6JU.js:10
notify @ index-BXGQS6JU.js:10
trigger @ index-BXGQS6JU.js:10
set value @ index-BXGQS6JU.js:10
M @ index-BXGQS6JU.js:63
(匿名) @ index-BXGQS6JU.js:63
Promise.then
O @ index-BXGQS6JU.js:63
w @ index-BXGQS6JU.js:63
install @ index-BXGQS6JU.js:63
use @ index-BXGQS6JU.js:14
(匿名) @ index-BXGQS6JU.js:63
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/assets"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-BXGQS6JU.js:20
xhr @ index-BXGQS6JU.js:20
Xg @ index-BXGQS6JU.js:22
Promise.then
_request @ index-BXGQS6JU.js:23
request @ index-BXGQS6JU.js:22
(匿名) @ index-BXGQS6JU.js:18
d @ asset-DOQtSa47.js:1
T @ OrderStatusBadge-CPr9zilU.js:1
initialize @ OrderStatusBadge-CPr9zilU.js:1
(匿名) @ OrderStatusBadge-CPr9zilU.js:1
await in (匿名)
(匿名) @ index-BXGQS6JU.js:14
Yi @ index-BXGQS6JU.js:14
Wo @ index-BXGQS6JU.js:14
t.__weh.t.__weh @ index-BXGQS6JU.js:14
A1 @ index-BXGQS6JU.js:14
L1 @ index-BXGQS6JU.js:14
Promise.then
P1 @ index-BXGQS6JU.js:14
gT @ index-BXGQS6JU.js:14
n$ @ index-BXGQS6JU.js:14
Zv.i.scheduler @ index-BXGQS6JU.js:14
d.scheduler @ index-BXGQS6JU.js:10
trigger @ index-BXGQS6JU.js:10
Bv @ index-BXGQS6JU.js:10
notify @ index-BXGQS6JU.js:10
trigger @ index-BXGQS6JU.js:10
set value @ index-BXGQS6JU.js:10
M @ index-BXGQS6JU.js:63
(匿名) @ index-BXGQS6JU.js:63
Promise.then
O @ index-BXGQS6JU.js:63
w @ index-BXGQS6JU.js:63
install @ index-BXGQS6JU.js:63
use @ index-BXGQS6JU.js:14
(匿名) @ index-BXGQS6JU.js:63
ServiceOrderForm-By_bhDrd.js:1 ServiceInfo组件接收到的数据: Proxy(Object) {order_id: '', order_category: '服务订单', enterprise_id: null, asset_id: null, user_id: null, …}
ServiceOrderForm-By_bhDrd.js:1 服务明细数据: Proxy(Array) {}
ServiceOrderForm-By_bhDrd.js:1 ServiceInfo组件接收到的数据: Proxy(Object) {service_items: Proxy(Array), standard_amount: 0, actual_amount: 0}
ServiceOrderForm-By_bhDrd.js:1 服务明细数据: Proxy(Array) {0: {…}}
ServiceOrderForm-By_bhDrd.js:1 ServiceOrderForm 组件挂载，当前模式: create 是否编辑: true
ServiceOrderForm-By_bhDrd.js:1 初始化新服务订单
OrderStatusBadge-CPr9zilU.js:1 订单编码生成成功: SO20250729CFT
OrderStatusBadge-CPr9zilU.js:1 OrderHeader updateParent 被调用，当前数据: {enterprise_id: null, user_id: null, is_partner_order: false, partner_user_id: null, commission_base: null, …}
ServiceOrderForm-By_bhDrd.js:1 ServiceInfo组件接收到的数据: Proxy(Object) {order_id: 'SO20250729CFT', order_category: '服务订单', enterprise_id: null, asset_id: null, user_id: null, …}
ServiceOrderForm-By_bhDrd.js:1 服务明细数据: Proxy(Array) {0: {…}}
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/orders/next-id?orderType=service"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-BXGQS6JU.js:20
xhr @ index-BXGQS6JU.js:20
Xg @ index-BXGQS6JU.js:22
Promise.then
_request @ index-BXGQS6JU.js:23
request @ index-BXGQS6JU.js:22
Ga.<computed> @ index-BXGQS6JU.js:23
(匿名) @ index-BXGQS6JU.js:18
c @ order-BLZdhLwT.js:1
ce @ OrderStatusBadge-CPr9zilU.js:1
(匿名) @ OrderStatusBadge-CPr9zilU.js:1
(匿名) @ index-BXGQS6JU.js:14
Yi @ index-BXGQS6JU.js:14
Wo @ index-BXGQS6JU.js:14
t.__weh.t.__weh @ index-BXGQS6JU.js:14
A1 @ index-BXGQS6JU.js:14
L1 @ index-BXGQS6JU.js:14
Promise.then
P1 @ index-BXGQS6JU.js:14
gT @ index-BXGQS6JU.js:14
n$ @ index-BXGQS6JU.js:14
Zv.i.scheduler @ index-BXGQS6JU.js:14
d.scheduler @ index-BXGQS6JU.js:10
trigger @ index-BXGQS6JU.js:10
Bv @ index-BXGQS6JU.js:10
notify @ index-BXGQS6JU.js:10
trigger @ index-BXGQS6JU.js:10
set value @ index-BXGQS6JU.js:10
M @ index-BXGQS6JU.js:63
(匿名) @ index-BXGQS6JU.js:63
Promise.then
O @ index-BXGQS6JU.js:63
w @ index-BXGQS6JU.js:63
w @ index-BXGQS6JU.js:40
p @ index-BXGQS6JU.js:40
Yi @ index-BXGQS6JU.js:14
Wo @ index-BXGQS6JU.js:14
n @ index-BXGQS6JU.js:18
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/enterprises"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-BXGQS6JU.js:20
xhr @ index-BXGQS6JU.js:20
Xg @ index-BXGQS6JU.js:22
Promise.then
_request @ index-BXGQS6JU.js:23
request @ index-BXGQS6JU.js:22
Ga.<computed> @ index-BXGQS6JU.js:23
(匿名) @ index-BXGQS6JU.js:18
vte @ index-BXGQS6JU.js:63
B @ OrderStatusBadge-CPr9zilU.js:1
initialize @ OrderStatusBadge-CPr9zilU.js:1
(匿名) @ OrderStatusBadge-CPr9zilU.js:1
await in (匿名)
(匿名) @ index-BXGQS6JU.js:14
Yi @ index-BXGQS6JU.js:14
Wo @ index-BXGQS6JU.js:14
t.__weh.t.__weh @ index-BXGQS6JU.js:14
A1 @ index-BXGQS6JU.js:14
L1 @ index-BXGQS6JU.js:14
Promise.then
P1 @ index-BXGQS6JU.js:14
gT @ index-BXGQS6JU.js:14
n$ @ index-BXGQS6JU.js:14
Zv.i.scheduler @ index-BXGQS6JU.js:14
d.scheduler @ index-BXGQS6JU.js:10
trigger @ index-BXGQS6JU.js:10
Bv @ index-BXGQS6JU.js:10
notify @ index-BXGQS6JU.js:10
trigger @ index-BXGQS6JU.js:10
set value @ index-BXGQS6JU.js:10
M @ index-BXGQS6JU.js:63
(匿名) @ index-BXGQS6JU.js:63
Promise.then
O @ index-BXGQS6JU.js:63
w @ index-BXGQS6JU.js:63
w @ index-BXGQS6JU.js:40
p @ index-BXGQS6JU.js:40
Yi @ index-BXGQS6JU.js:14
Wo @ index-BXGQS6JU.js:14
n @ index-BXGQS6JU.js:18
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/users"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-BXGQS6JU.js:20
xhr @ index-BXGQS6JU.js:20
Xg @ index-BXGQS6JU.js:22
Promise.then
_request @ index-BXGQS6JU.js:23
request @ index-BXGQS6JU.js:22
Ga.<computed> @ index-BXGQS6JU.js:23
(匿名) @ index-BXGQS6JU.js:18
yte @ index-BXGQS6JU.js:63
O @ OrderStatusBadge-CPr9zilU.js:1
initialize @ OrderStatusBadge-CPr9zilU.js:1
(匿名) @ OrderStatusBadge-CPr9zilU.js:1
await in (匿名)
(匿名) @ index-BXGQS6JU.js:14
Yi @ index-BXGQS6JU.js:14
Wo @ index-BXGQS6JU.js:14
t.__weh.t.__weh @ index-BXGQS6JU.js:14
A1 @ index-BXGQS6JU.js:14
L1 @ index-BXGQS6JU.js:14
Promise.then
P1 @ index-BXGQS6JU.js:14
gT @ index-BXGQS6JU.js:14
n$ @ index-BXGQS6JU.js:14
Zv.i.scheduler @ index-BXGQS6JU.js:14
d.scheduler @ index-BXGQS6JU.js:10
trigger @ index-BXGQS6JU.js:10
Bv @ index-BXGQS6JU.js:10
notify @ index-BXGQS6JU.js:10
trigger @ index-BXGQS6JU.js:10
set value @ index-BXGQS6JU.js:10
M @ index-BXGQS6JU.js:63
(匿名) @ index-BXGQS6JU.js:63
Promise.then
O @ index-BXGQS6JU.js:63
w @ index-BXGQS6JU.js:63
w @ index-BXGQS6JU.js:40
p @ index-BXGQS6JU.js:40
Yi @ index-BXGQS6JU.js:14
Wo @ index-BXGQS6JU.js:14
n @ index-BXGQS6JU.js:18
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/assets"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-BXGQS6JU.js:20
xhr @ index-BXGQS6JU.js:20
Xg @ index-BXGQS6JU.js:22
Promise.then
_request @ index-BXGQS6JU.js:23
request @ index-BXGQS6JU.js:22
(匿名) @ index-BXGQS6JU.js:18
d @ asset-DOQtSa47.js:1
T @ OrderStatusBadge-CPr9zilU.js:1
initialize @ OrderStatusBadge-CPr9zilU.js:1
(匿名) @ OrderStatusBadge-CPr9zilU.js:1
await in (匿名)
(匿名) @ index-BXGQS6JU.js:14
Yi @ index-BXGQS6JU.js:14
Wo @ index-BXGQS6JU.js:14
t.__weh.t.__weh @ index-BXGQS6JU.js:14
A1 @ index-BXGQS6JU.js:14
L1 @ index-BXGQS6JU.js:14
Promise.then
P1 @ index-BXGQS6JU.js:14
gT @ index-BXGQS6JU.js:14
n$ @ index-BXGQS6JU.js:14
Zv.i.scheduler @ index-BXGQS6JU.js:14
d.scheduler @ index-BXGQS6JU.js:10
trigger @ index-BXGQS6JU.js:10
Bv @ index-BXGQS6JU.js:10
notify @ index-BXGQS6JU.js:10
trigger @ index-BXGQS6JU.js:10
set value @ index-BXGQS6JU.js:10
M @ index-BXGQS6JU.js:63
(匿名) @ index-BXGQS6JU.js:63
Promise.then
O @ index-BXGQS6JU.js:63
w @ index-BXGQS6JU.js:63
w @ index-BXGQS6JU.js:40
p @ index-BXGQS6JU.js:40
Yi @ index-BXGQS6JU.js:14
Wo @ index-BXGQS6JU.js:14
n @ index-BXGQS6JU.js:18
ProductOrderForm-C-g5v4bk.js:1 监听到价格字段变化: {standardPrice: 0, actualPrice: 0}
ProductOrderForm-C-g5v4bk.js:1 ProductOrderForm orderData 企业/用户ID变化: {enterprise_id: null, user_id: null, enterprise_type: 'object', user_type: 'object'}
ProductOrderForm-C-g5v4bk.js:1 ProductOrderForm 组件挂载，当前模式: create 是否编辑: true
ProductOrderForm-C-g5v4bk.js:1 初始化新产品订单
OrderStatusBadge-CPr9zilU.js:1 订单编码生成成功: PO20250729BKN
OrderStatusBadge-CPr9zilU.js:1 OrderHeader updateParent 被调用，当前数据: {enterprise_id: null, user_id: null, is_partner_order: false, partner_user_id: null, commission_base: null, …}
ProductOrderForm-C-g5v4bk.js:1 表头数据更新: {order_id: 'PO20250729BKN', order_category: '产品订单', enterprise_id: null, asset_id: null, user_id: null, …}
ProductOrderForm-C-g5v4bk.js:1 监听到产品信息变化: Proxy(Object) {product_id: null, user_count: 1, account_count: 1, duration_months: 12, selected_features: Array(0), …}
ProductOrderForm-C-g5v4bk.js:1 更新后的订单金额: {standard_amount: 0, actual_amount: 0}
ProductOrderForm-C-g5v4bk.js:1 监听到价格字段变化: {standardPrice: 0, actualPrice: 0}
ProductOrderForm-C-g5v4bk.js:1 ProductOrderForm orderData 企业/用户ID变化: {enterprise_id: null, user_id: null, enterprise_type: 'object', user_type: 'object'}
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/orders/next-id?orderType=product"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-BXGQS6JU.js:20
xhr @ index-BXGQS6JU.js:20
Xg @ index-BXGQS6JU.js:22
Promise.then
_request @ index-BXGQS6JU.js:23
request @ index-BXGQS6JU.js:22
Ga.<computed> @ index-BXGQS6JU.js:23
(匿名) @ index-BXGQS6JU.js:18
c @ order-BLZdhLwT.js:1
ce @ OrderStatusBadge-CPr9zilU.js:1
(匿名) @ OrderStatusBadge-CPr9zilU.js:1
(匿名) @ index-BXGQS6JU.js:14
Yi @ index-BXGQS6JU.js:14
Wo @ index-BXGQS6JU.js:14
t.__weh.t.__weh @ index-BXGQS6JU.js:14
A1 @ index-BXGQS6JU.js:14
L1 @ index-BXGQS6JU.js:14
Promise.then
P1 @ index-BXGQS6JU.js:14
gT @ index-BXGQS6JU.js:14
n$ @ index-BXGQS6JU.js:14
Zv.i.scheduler @ index-BXGQS6JU.js:14
d.scheduler @ index-BXGQS6JU.js:10
trigger @ index-BXGQS6JU.js:10
Bv @ index-BXGQS6JU.js:10
notify @ index-BXGQS6JU.js:10
trigger @ index-BXGQS6JU.js:10
set value @ index-BXGQS6JU.js:10
M @ index-BXGQS6JU.js:63
(匿名) @ index-BXGQS6JU.js:63
Promise.then
O @ index-BXGQS6JU.js:63
w @ index-BXGQS6JU.js:63
w @ index-BXGQS6JU.js:40
p @ index-BXGQS6JU.js:40
Yi @ index-BXGQS6JU.js:14
Wo @ index-BXGQS6JU.js:14
n @ index-BXGQS6JU.js:18
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/products"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-BXGQS6JU.js:20
xhr @ index-BXGQS6JU.js:20
Xg @ index-BXGQS6JU.js:22
Promise.then
_request @ index-BXGQS6JU.js:23
request @ index-BXGQS6JU.js:22
Ga.<computed> @ index-BXGQS6JU.js:23
(匿名) @ index-BXGQS6JU.js:18
$ @ product-elNrP0D8.js:1
V @ ProductOrderForm-C-g5v4bk.js:1
(匿名) @ ProductOrderForm-C-g5v4bk.js:1
(匿名) @ index-BXGQS6JU.js:14
Yi @ index-BXGQS6JU.js:14
Wo @ index-BXGQS6JU.js:14
t.__weh.t.__weh @ index-BXGQS6JU.js:14
A1 @ index-BXGQS6JU.js:14
L1 @ index-BXGQS6JU.js:14
Promise.then
P1 @ index-BXGQS6JU.js:14
gT @ index-BXGQS6JU.js:14
n$ @ index-BXGQS6JU.js:14
Zv.i.scheduler @ index-BXGQS6JU.js:14
d.scheduler @ index-BXGQS6JU.js:10
trigger @ index-BXGQS6JU.js:10
Bv @ index-BXGQS6JU.js:10
notify @ index-BXGQS6JU.js:10
trigger @ index-BXGQS6JU.js:10
set value @ index-BXGQS6JU.js:10
M @ index-BXGQS6JU.js:63
(匿名) @ index-BXGQS6JU.js:63
Promise.then
O @ index-BXGQS6JU.js:63
w @ index-BXGQS6JU.js:63
w @ index-BXGQS6JU.js:40
p @ index-BXGQS6JU.js:40
Yi @ index-BXGQS6JU.js:14
Wo @ index-BXGQS6JU.js:14
n @ index-BXGQS6JU.js:18
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/enterprises"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-BXGQS6JU.js:20
xhr @ index-BXGQS6JU.js:20
Xg @ index-BXGQS6JU.js:22
Promise.then
_request @ index-BXGQS6JU.js:23
request @ index-BXGQS6JU.js:22
Ga.<computed> @ index-BXGQS6JU.js:23
(匿名) @ index-BXGQS6JU.js:18
vte @ index-BXGQS6JU.js:63
B @ OrderStatusBadge-CPr9zilU.js:1
initialize @ OrderStatusBadge-CPr9zilU.js:1
(匿名) @ OrderStatusBadge-CPr9zilU.js:1
await in (匿名)
(匿名) @ index-BXGQS6JU.js:14
Yi @ index-BXGQS6JU.js:14
Wo @ index-BXGQS6JU.js:14
t.__weh.t.__weh @ index-BXGQS6JU.js:14
A1 @ index-BXGQS6JU.js:14
L1 @ index-BXGQS6JU.js:14
Promise.then
P1 @ index-BXGQS6JU.js:14
gT @ index-BXGQS6JU.js:14
n$ @ index-BXGQS6JU.js:14
Zv.i.scheduler @ index-BXGQS6JU.js:14
d.scheduler @ index-BXGQS6JU.js:10
trigger @ index-BXGQS6JU.js:10
Bv @ index-BXGQS6JU.js:10
notify @ index-BXGQS6JU.js:10
trigger @ index-BXGQS6JU.js:10
set value @ index-BXGQS6JU.js:10
M @ index-BXGQS6JU.js:63
(匿名) @ index-BXGQS6JU.js:63
Promise.then
O @ index-BXGQS6JU.js:63
w @ index-BXGQS6JU.js:63
w @ index-BXGQS6JU.js:40
p @ index-BXGQS6JU.js:40
Yi @ index-BXGQS6JU.js:14
Wo @ index-BXGQS6JU.js:14
n @ index-BXGQS6JU.js:18
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/assets"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-BXGQS6JU.js:20
xhr @ index-BXGQS6JU.js:20
Xg @ index-BXGQS6JU.js:22
Promise.then
_request @ index-BXGQS6JU.js:23
request @ index-BXGQS6JU.js:22
(匿名) @ index-BXGQS6JU.js:18
d @ asset-DOQtSa47.js:1
T @ OrderStatusBadge-CPr9zilU.js:1
initialize @ OrderStatusBadge-CPr9zilU.js:1
(匿名) @ OrderStatusBadge-CPr9zilU.js:1
await in (匿名)
(匿名) @ index-BXGQS6JU.js:14
Yi @ index-BXGQS6JU.js:14
Wo @ index-BXGQS6JU.js:14
t.__weh.t.__weh @ index-BXGQS6JU.js:14
A1 @ index-BXGQS6JU.js:14
L1 @ index-BXGQS6JU.js:14
Promise.then
P1 @ index-BXGQS6JU.js:14
gT @ index-BXGQS6JU.js:14
n$ @ index-BXGQS6JU.js:14
Zv.i.scheduler @ index-BXGQS6JU.js:14
d.scheduler @ index-BXGQS6JU.js:10
trigger @ index-BXGQS6JU.js:10
Bv @ index-BXGQS6JU.js:10
notify @ index-BXGQS6JU.js:10
trigger @ index-BXGQS6JU.js:10
set value @ index-BXGQS6JU.js:10
M @ index-BXGQS6JU.js:63
(匿名) @ index-BXGQS6JU.js:63
Promise.then
O @ index-BXGQS6JU.js:63
w @ index-BXGQS6JU.js:63
w @ index-BXGQS6JU.js:40
p @ index-BXGQS6JU.js:40
Yi @ index-BXGQS6JU.js:14
Wo @ index-BXGQS6JU.js:14
n @ index-BXGQS6JU.js:18
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/users"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-BXGQS6JU.js:20
xhr @ index-BXGQS6JU.js:20
Xg @ index-BXGQS6JU.js:22
Promise.then
_request @ index-BXGQS6JU.js:23
request @ index-BXGQS6JU.js:22
Ga.<computed> @ index-BXGQS6JU.js:23
(匿名) @ index-BXGQS6JU.js:18
yte @ index-BXGQS6JU.js:63
O @ OrderStatusBadge-CPr9zilU.js:1
initialize @ OrderStatusBadge-CPr9zilU.js:1
(匿名) @ OrderStatusBadge-CPr9zilU.js:1
await in (匿名)
(匿名) @ index-BXGQS6JU.js:14
Yi @ index-BXGQS6JU.js:14
Wo @ index-BXGQS6JU.js:14
t.__weh.t.__weh @ index-BXGQS6JU.js:14
A1 @ index-BXGQS6JU.js:14
L1 @ index-BXGQS6JU.js:14
Promise.then
P1 @ index-BXGQS6JU.js:14
gT @ index-BXGQS6JU.js:14
n$ @ index-BXGQS6JU.js:14
Zv.i.scheduler @ index-BXGQS6JU.js:14
d.scheduler @ index-BXGQS6JU.js:10
trigger @ index-BXGQS6JU.js:10
Bv @ index-BXGQS6JU.js:10
notify @ index-BXGQS6JU.js:10
trigger @ index-BXGQS6JU.js:10
set value @ index-BXGQS6JU.js:10
M @ index-BXGQS6JU.js:63
(匿名) @ index-BXGQS6JU.js:63
Promise.then
O @ index-BXGQS6JU.js:63
w @ index-BXGQS6JU.js:63
w @ index-BXGQS6JU.js:40
p @ index-BXGQS6JU.js:40
Yi @ index-BXGQS6JU.js:14
Wo @ index-BXGQS6JU.js:14
n @ index-BXGQS6JU.js:18
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/users"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-BXGQS6JU.js:20
xhr @ index-BXGQS6JU.js:20
Xg @ index-BXGQS6JU.js:22
Promise.then
_request @ index-BXGQS6JU.js:23
request @ index-BXGQS6JU.js:22
Ga.<computed> @ index-BXGQS6JU.js:23
(匿名) @ index-BXGQS6JU.js:18
yte @ index-BXGQS6JU.js:63
O @ OrderStatusBadge-CPr9zilU.js:1
initialize @ OrderStatusBadge-CPr9zilU.js:1
Yi @ index-BXGQS6JU.js:14
Wo @ index-BXGQS6JU.js:14
JT @ index-BXGQS6JU.js:14
d @ index-BXGQS6JU.js:23
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/enterprises"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-BXGQS6JU.js:20
xhr @ index-BXGQS6JU.js:20
Xg @ index-BXGQS6JU.js:22
Promise.then
_request @ index-BXGQS6JU.js:23
request @ index-BXGQS6JU.js:22
Ga.<computed> @ index-BXGQS6JU.js:23
(匿名) @ index-BXGQS6JU.js:18
vte @ index-BXGQS6JU.js:63
B @ OrderStatusBadge-CPr9zilU.js:1
initialize @ OrderStatusBadge-CPr9zilU.js:1
Yi @ index-BXGQS6JU.js:14
Wo @ index-BXGQS6JU.js:14
JT @ index-BXGQS6JU.js:14
d @ index-BXGQS6JU.js:23
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/assets"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-BXGQS6JU.js:20
xhr @ index-BXGQS6JU.js:20
Xg @ index-BXGQS6JU.js:22
Promise.then
_request @ index-BXGQS6JU.js:23
request @ index-BXGQS6JU.js:22
(匿名) @ index-BXGQS6JU.js:18
d @ asset-DOQtSa47.js:1
T @ OrderStatusBadge-CPr9zilU.js:1
initialize @ OrderStatusBadge-CPr9zilU.js:1
Yi @ index-BXGQS6JU.js:14
Wo @ index-BXGQS6JU.js:14
JT @ index-BXGQS6JU.js:14
d @ index-BXGQS6JU.js:23
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/enterprises"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-BXGQS6JU.js:20
xhr @ index-BXGQS6JU.js:20
Xg @ index-BXGQS6JU.js:22
Promise.then
_request @ index-BXGQS6JU.js:23
request @ index-BXGQS6JU.js:22
Ga.<computed> @ index-BXGQS6JU.js:23
(匿名) @ index-BXGQS6JU.js:18
vte @ index-BXGQS6JU.js:63
B @ OrderStatusBadge-CPr9zilU.js:1
searchEnterprises @ OrderStatusBadge-CPr9zilU.js:1
Ce @ index-BXGQS6JU.js:40
(匿名) @ index-BXGQS6JU.js:40
Yi @ index-BXGQS6JU.js:14
Wo @ index-BXGQS6JU.js:14
Zv.i.call @ index-BXGQS6JU.js:14
b @ index-BXGQS6JU.js:10
Yi @ index-BXGQS6JU.js:14
L1 @ index-BXGQS6JU.js:14
Promise.then
P1 @ index-BXGQS6JU.js:14
jv @ index-BXGQS6JU.js:14
Zv.i.scheduler @ index-BXGQS6JU.js:14
d.scheduler @ index-BXGQS6JU.js:10
trigger @ index-BXGQS6JU.js:10
Bv @ index-BXGQS6JU.js:10
notify @ index-BXGQS6JU.js:10
trigger @ index-BXGQS6JU.js:10
set value @ index-BXGQS6JU.js:10
kt @ index-BXGQS6JU.js:40
Ge.n.<computed>.n.<computed> @ index-BXGQS6JU.js:18
Yi @ index-BXGQS6JU.js:14
Wo @ index-BXGQS6JU.js:14
n @ index-BXGQS6JU.js:18
OrderStatusBadge-CPr9zilU.js:1 企业变化处理: {原始值: 11, 清理后: 11, 类型: 'number'}
OrderStatusBadge-CPr9zilU.js:1 OrderHeader updateParent 被调用，当前数据: {enterprise_id: 11, user_id: null, is_partner_order: false, partner_user_id: null, commission_base: null, …}
ProductOrderForm-C-g5v4bk.js:1 表头数据更新: {order_id: 'PO20250729BKN', order_category: '产品订单', enterprise_id: 11, asset_id: null, user_id: null, …}
ProductOrderForm-C-g5v4bk.js:1 监听到产品信息变化: Proxy(Object) {product_id: null, user_count: 1, account_count: 1, duration_months: 12, selected_features: Array(0), …}
ProductOrderForm-C-g5v4bk.js:1 更新后的订单金额: {standard_amount: 0, actual_amount: 0}
ProductOrderForm-C-g5v4bk.js:1 监听到价格字段变化: {standardPrice: 0, actualPrice: 0}
ProductOrderForm-C-g5v4bk.js:1 ProductOrderForm orderData 企业/用户ID变化: {enterprise_id: 11, user_id: null, enterprise_type: 'number', user_type: 'object'}
OrderStatusBadge-CPr9zilU.js:1 企业字段验证: {value: 11, enterpriseId: 11, userId: null, enterpriseType: 'number', userType: 'object'}
OrderStatusBadge-CPr9zilU.js:1 用户字段验证: {value: null, userId: null, enterpriseId: 11, userType: 'object', enterpriseType: 'number'}
OrderStatusBadge-CPr9zilU.js:1 企业字段验证: {value: 11, enterpriseId: 11, userId: null, enterpriseType: 'number', userType: 'object'}
OrderStatusBadge-CPr9zilU.js:1 OrderHeader updateParent 被调用，当前数据: {enterprise_id: 11, user_id: null, is_partner_order: false, partner_user_id: null, commission_base: null, …}
ProductOrderForm-C-g5v4bk.js:1 表头数据更新: {order_id: 'PO20250729BKN', order_category: '产品订单', enterprise_id: 11, asset_id: null, user_id: null, …}
ProductOrderForm-C-g5v4bk.js:1 监听到产品信息变化: Proxy(Object) {product_id: null, user_count: 1, account_count: 1, duration_months: 12, selected_features: Array(0), …}
ProductOrderForm-C-g5v4bk.js:1 更新后的订单金额: {standard_amount: 0, actual_amount: 0}
ProductOrderForm-C-g5v4bk.js:1 监听到价格字段变化: {standardPrice: 0, actualPrice: 0}
ProductOrderForm-C-g5v4bk.js:1 ProductOrderForm orderData 企业/用户ID变化: {enterprise_id: 11, user_id: null, enterprise_type: 'number', user_type: 'object'}
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/assets?enterprise_id=11"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-BXGQS6JU.js:20
xhr @ index-BXGQS6JU.js:20
Xg @ index-BXGQS6JU.js:22
Promise.then
_request @ index-BXGQS6JU.js:23
request @ index-BXGQS6JU.js:22
(匿名) @ index-BXGQS6JU.js:18
d @ asset-DOQtSa47.js:1
T @ OrderStatusBadge-CPr9zilU.js:1
handleEnterpriseChange @ OrderStatusBadge-CPr9zilU.js:1
pe @ OrderStatusBadge-CPr9zilU.js:1
Yi @ index-BXGQS6JU.js:14
Wo @ index-BXGQS6JU.js:14
JT @ index-BXGQS6JU.js:14
mt @ index-BXGQS6JU.js:40
ye @ index-BXGQS6JU.js:40
m @ index-BXGQS6JU.js:40
Ge.n.<computed>.n.<computed> @ index-BXGQS6JU.js:18
Yi @ index-BXGQS6JU.js:14
Wo @ index-BXGQS6JU.js:14
n @ index-BXGQS6JU.js:18
OrderStatusBadge-CPr9zilU.js:1 企业字段验证: {value: 11, enterpriseId: 11, userId: null, enterpriseType: 'number', userType: 'object'}
OrderStatusBadge-CPr9zilU.js:1 OrderHeader updateParent 被调用，当前数据: {enterprise_id: 11, user_id: null, is_partner_order: false, partner_user_id: null, commission_base: null, …}
ProductOrderForm-C-g5v4bk.js:1 表头数据更新: {order_id: 'PO20250729BKN', order_category: '产品订单', enterprise_id: 11, asset_id: null, user_id: null, …}
ProductOrderForm-C-g5v4bk.js:1 监听到产品信息变化: Proxy(Object) {product_id: null, user_count: 1, account_count: 1, duration_months: 12, selected_features: Array(0), …}
ProductOrderForm-C-g5v4bk.js:1 更新后的订单金额: {standard_amount: 0, actual_amount: 0}
ProductOrderForm-C-g5v4bk.js:1 监听到价格字段变化: {standardPrice: 0, actualPrice: 0}
ProductOrderForm-C-g5v4bk.js:1 ProductOrderForm orderData 企业/用户ID变化: {enterprise_id: 11, user_id: null, enterprise_type: 'number', user_type: 'object'}
OrderStatusBadge-CPr9zilU.js:1 OrderHeader updateParent 被调用，当前数据: {enterprise_id: 11, user_id: null, is_partner_order: true, partner_user_id: null, commission_base: null, …}
ProductOrderForm-C-g5v4bk.js:1 表头数据更新: {order_id: 'PO20250729BKN', order_category: '产品订单', enterprise_id: 11, asset_id: null, user_id: null, …}
ProductOrderForm-C-g5v4bk.js:1 监听到产品信息变化: Proxy(Object) {product_id: null, user_count: 1, account_count: 1, duration_months: 12, selected_features: Array(0), …}
ProductOrderForm-C-g5v4bk.js:1 更新后的订单金额: {standard_amount: 0, actual_amount: 0}
ProductOrderForm-C-g5v4bk.js:1 监听到价格字段变化: {standardPrice: 0, actualPrice: 0}
ProductOrderForm-C-g5v4bk.js:1 ProductOrderForm orderData 企业/用户ID变化: {enterprise_id: 11, user_id: null, enterprise_type: 'number', user_type: 'object'}
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/users?q="。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-BXGQS6JU.js:20
xhr @ index-BXGQS6JU.js:20
Xg @ index-BXGQS6JU.js:22
Promise.then
_request @ index-BXGQS6JU.js:23
request @ index-BXGQS6JU.js:22
Ga.<computed> @ index-BXGQS6JU.js:23
(匿名) @ index-BXGQS6JU.js:18
yte @ index-BXGQS6JU.js:63
te @ OrderStatusBadge-CPr9zilU.js:1
J @ OrderStatusBadge-CPr9zilU.js:1
Yi @ index-BXGQS6JU.js:14
Wo @ index-BXGQS6JU.js:14
JT @ index-BXGQS6JU.js:14
d @ index-BXGQS6JU.js:23
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/users?q="。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-BXGQS6JU.js:20
xhr @ index-BXGQS6JU.js:20
Xg @ index-BXGQS6JU.js:22
Promise.then
_request @ index-BXGQS6JU.js:23
request @ index-BXGQS6JU.js:22
Ga.<computed> @ index-BXGQS6JU.js:23
(匿名) @ index-BXGQS6JU.js:18
yte @ index-BXGQS6JU.js:63
te @ OrderStatusBadge-CPr9zilU.js:1
J @ OrderStatusBadge-CPr9zilU.js:1
Yi @ index-BXGQS6JU.js:14
Wo @ index-BXGQS6JU.js:14
JT @ index-BXGQS6JU.js:14
d @ index-BXGQS6JU.js:23
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/users?q="。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-BXGQS6JU.js:20
xhr @ index-BXGQS6JU.js:20
Xg @ index-BXGQS6JU.js:22
Promise.then
_request @ index-BXGQS6JU.js:23
request @ index-BXGQS6JU.js:22
Ga.<computed> @ index-BXGQS6JU.js:23
(匿名) @ index-BXGQS6JU.js:18
yte @ index-BXGQS6JU.js:63
te @ OrderStatusBadge-CPr9zilU.js:1
Ce @ index-BXGQS6JU.js:40
(匿名) @ index-BXGQS6JU.js:40
Yi @ index-BXGQS6JU.js:14
Wo @ index-BXGQS6JU.js:14
Zv.i.call @ index-BXGQS6JU.js:14
b @ index-BXGQS6JU.js:10
Yi @ index-BXGQS6JU.js:14
L1 @ index-BXGQS6JU.js:14
Promise.then
P1 @ index-BXGQS6JU.js:14
jv @ index-BXGQS6JU.js:14
Zv.i.scheduler @ index-BXGQS6JU.js:14
d.scheduler @ index-BXGQS6JU.js:10
trigger @ index-BXGQS6JU.js:10
Bv @ index-BXGQS6JU.js:10
notify @ index-BXGQS6JU.js:10
trigger @ index-BXGQS6JU.js:10
set value @ index-BXGQS6JU.js:10
kt @ index-BXGQS6JU.js:40
Ge.n.<computed>.n.<computed> @ index-BXGQS6JU.js:18
Yi @ index-BXGQS6JU.js:14
Wo @ index-BXGQS6JU.js:14
n @ index-BXGQS6JU.js:18
OrderStatusBadge-CPr9zilU.js:1 加载合伙人信息，ID: 15
OrderStatusBadge-CPr9zilU.js:1 合伙人已在列表中: 张小草
OrderStatusBadge-CPr9zilU.js:1 OrderHeader updateParent 被调用，当前数据: {enterprise_id: 11, user_id: null, is_partner_order: true, partner_user_id: 15, commission_base: '0.2000', …}
ProductOrderForm-C-g5v4bk.js:1 表头数据更新: {order_id: 'PO20250729BKN', order_category: '产品订单', enterprise_id: 11, asset_id: null, user_id: null, …}
ProductOrderForm-C-g5v4bk.js:1 监听到产品信息变化: Proxy(Object) {product_id: null, user_count: 1, account_count: 1, duration_months: 12, selected_features: Array(0), …}
ProductOrderForm-C-g5v4bk.js:1 更新后的订单金额: {standard_amount: 0, actual_amount: 0}
ProductOrderForm-C-g5v4bk.js:1 监听到价格字段变化: {standardPrice: 0, actualPrice: 0}
ProductOrderForm-C-g5v4bk.js:1 ProductOrderForm orderData 企业/用户ID变化: {enterprise_id: 11, user_id: null, enterprise_type: 'number', user_type: 'object'}
OrderStatusBadge-CPr9zilU.js:1 加载合伙人信息，ID: 15
OrderStatusBadge-CPr9zilU.js:1 合伙人已在列表中: 张小草
OrderStatusBadge-CPr9zilU.js:1 OrderHeader updateParent 被调用，当前数据: {enterprise_id: 11, user_id: null, is_partner_order: true, partner_user_id: 15, commission_base: '0.2000', …}
ProductOrderForm-C-g5v4bk.js:1 表头数据更新: {order_id: 'PO20250729BKN', order_category: '产品订单', enterprise_id: 11, asset_id: null, user_id: null, …}
ProductOrderForm-C-g5v4bk.js:1 监听到产品信息变化: Proxy(Object) {product_id: null, user_count: 1, account_count: 1, duration_months: 12, selected_features: Array(0), …}
ProductOrderForm-C-g5v4bk.js:1 更新后的订单金额: {standard_amount: 0, actual_amount: 0}
ProductOrderForm-C-g5v4bk.js:1 监听到价格字段变化: {standardPrice: 0, actualPrice: 0}
ProductOrderForm-C-g5v4bk.js:1 ProductOrderForm orderData 企业/用户ID变化: {enterprise_id: 11, user_id: null, enterprise_type: 'number', user_type: 'object'}
OrderStatusBadge-CPr9zilU.js:1 OrderHeader updateParent 被调用，当前数据: {enterprise_id: 11, user_id: null, is_partner_order: true, partner_user_id: 15, commission_base: '0.2000', …}
ProductOrderForm-C-g5v4bk.js:1 表头数据更新: {order_id: 'PO20250729BKN', order_category: '产品订单', enterprise_id: 11, asset_id: null, user_id: null, …}
OrderStatusBadge-CPr9zilU.js:1 OrderHeader updateParent 被调用，当前数据: {enterprise_id: 11, user_id: null, is_partner_order: true, partner_user_id: 15, commission_base: '0.2000', …}
ProductOrderForm-C-g5v4bk.js:1 表头数据更新: {order_id: 'PO20250729BKN', order_category: '产品订单', enterprise_id: 11, asset_id: null, user_id: null, …}
ProductOrderForm-C-g5v4bk.js:1 监听到产品信息变化: Proxy(Object) {product_id: null, user_count: 1, account_count: 1, duration_months: 12, selected_features: Array(0), …}
ProductOrderForm-C-g5v4bk.js:1 更新后的订单金额: {standard_amount: 0, actual_amount: 0}
ProductOrderForm-C-g5v4bk.js:1 监听到价格字段变化: {standardPrice: 0, actualPrice: 0}
ProductOrderForm-C-g5v4bk.js:1 ProductOrderForm orderData 企业/用户ID变化: {enterprise_id: 11, user_id: null, enterprise_type: 'number', user_type: 'object'}
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/products/9/features"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-BXGQS6JU.js:20
xhr @ index-BXGQS6JU.js:20
Xg @ index-BXGQS6JU.js:22
Promise.then
_request @ index-BXGQS6JU.js:23
request @ index-BXGQS6JU.js:22
Ga.<computed> @ index-BXGQS6JU.js:23
(匿名) @ index-BXGQS6JU.js:18
a @ product-elNrP0D8.js:1
A @ product-elNrP0D8.js:1
j @ ProductOrderForm-C-g5v4bk.js:1
G @ ProductOrderForm-C-g5v4bk.js:1
Yi @ index-BXGQS6JU.js:14
Wo @ index-BXGQS6JU.js:14
JT @ index-BXGQS6JU.js:14
mt @ index-BXGQS6JU.js:40
ye @ index-BXGQS6JU.js:40
m @ index-BXGQS6JU.js:40
Ge.n.<computed>.n.<computed> @ index-BXGQS6JU.js:18
Yi @ index-BXGQS6JU.js:14
Wo @ index-BXGQS6JU.js:14
n @ index-BXGQS6JU.js:18
ProductOrderForm-C-g5v4bk.js:1 ProductInfo updateParent 被调用，当前数据: Proxy(Object) {product_id: 9, user_count: 1, account_count: 3, duration_months: 12, selected_features: Array(0), …}
ProductOrderForm-C-g5v4bk.js:1 监听到产品信息变化: Proxy(Object) {product_id: 9, user_count: 1, account_count: 3, duration_months: 12, selected_features: Proxy(Array), …}
ProductOrderForm-C-g5v4bk.js:1 更新后的订单金额: {standard_amount: 898, actual_amount: 898}
ProductOrderForm-C-g5v4bk.js:1 监听到价格字段变化: {standardPrice: 898, actualPrice: 898}
ProductOrderForm-C-g5v4bk.js:1 ProductInfo updateParent 被调用，当前数据: Proxy(Object) {product_id: 9, user_count: 1, account_count: 3, duration_months: 12, selected_features: Array(0), …}
OrderStatusBadge-CPr9zilU.js:1 自动计算分润金额: {baseRatio: 0.2, extraRatio: 0.1, totalRatio: 0.*****************, actualAmount: 898, commissionAmount: 269.4}
ProductOrderForm-C-g5v4bk.js:1 监听到产品信息变化: Proxy(Object) {product_id: 9, user_count: 1, account_count: 3, duration_months: 12, selected_features: Proxy(Array), …}
ProductOrderForm-C-g5v4bk.js:1 更新后的订单金额: {standard_amount: 898, actual_amount: 898}
ProductOrderForm-C-g5v4bk.js:1 监听到价格字段变化: {standardPrice: 898, actualPrice: 898}
OrderStatusBadge-CPr9zilU.js:1 OrderHeader updateParent 被调用，当前数据: {enterprise_id: 11, user_id: null, is_partner_order: true, partner_user_id: 15, commission_base: '0.2000', …}
ProductOrderForm-C-g5v4bk.js:1 表头数据更新: {order_id: 'PO20250729BKN', order_category: '产品订单', enterprise_id: 11, asset_id: null, user_id: null, …}
ProductOrderForm-C-g5v4bk.js:1 监听到产品信息变化: Proxy(Object) {product_id: 9, user_count: 1, account_count: 3, duration_months: 12, selected_features: Proxy(Array), …}
ProductOrderForm-C-g5v4bk.js:1 更新后的订单金额: {standard_amount: 898, actual_amount: 898}
ProductOrderForm-C-g5v4bk.js:1 监听到价格字段变化: {standardPrice: 898, actualPrice: 898}
ProductOrderForm-C-g5v4bk.js:1 ProductOrderForm orderData 企业/用户ID变化: {enterprise_id: 11, user_id: null, enterprise_type: 'number', user_type: 'object'}
OrderStatusBadge-CPr9zilU.js:1 自动计算分润金额: {baseRatio: 0.2, extraRatio: 0.1, totalRatio: 0.*****************, actualAmount: 898, commissionAmount: 269.4}
OrderStatusBadge-CPr9zilU.js:1 自动计算分润金额: {baseRatio: 0.2, extraRatio: 0.1, totalRatio: 0.*****************, actualAmount: 898, commissionAmount: 269.4}
inspector.js:7 XHR 已完成加载:POST "https://service.bogoo.net/api/pricing/calculate"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-BXGQS6JU.js:20
xhr @ index-BXGQS6JU.js:20
Xg @ index-BXGQS6JU.js:22
Promise.then
_request @ index-BXGQS6JU.js:23
request @ index-BXGQS6JU.js:22
(匿名) @ index-BXGQS6JU.js:23
(匿名) @ index-BXGQS6JU.js:18
oe @ ProductOrderForm-C-g5v4bk.js:1
b @ ProductOrderForm-C-g5v4bk.js:1
G @ ProductOrderForm-C-g5v4bk.js:1
await in G
Yi @ index-BXGQS6JU.js:14
Wo @ index-BXGQS6JU.js:14
JT @ index-BXGQS6JU.js:14
mt @ index-BXGQS6JU.js:40
ye @ index-BXGQS6JU.js:40
m @ index-BXGQS6JU.js:40
Ge.n.<computed>.n.<computed> @ index-BXGQS6JU.js:18
Yi @ index-BXGQS6JU.js:14
Wo @ index-BXGQS6JU.js:14
n @ index-BXGQS6JU.js:18
ProductOrderForm-C-g5v4bk.js:1 保存前的完整订单数据: {user_id: null, enterprise_id: 11, user_id_type: 'object', enterprise_id_type: 'number', is_partner_order: true, …}
OrderStatusBadge-CPr9zilU.js:1 企业字段验证: {value: 11, enterpriseId: 11, userId: null, enterpriseType: 'number', userType: 'object'}
OrderStatusBadge-CPr9zilU.js:1 用户字段验证: {value: null, userId: null, enterpriseId: 11, userType: 'object', enterpriseType: 'number'}
ProductOrderForm-C-g5v4bk.js:1 准备创建产品订单，数据: {user_id: null, enterprise_id: 11, partner_user_id: 15, partner_user_id_type: 'number', is_partner_order: true, …}
inspector.js:7 XHR 已完成加载:POST "https://service.bogoo.net/api/orders/product"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-BXGQS6JU.js:20
xhr @ index-BXGQS6JU.js:20
Xg @ index-BXGQS6JU.js:22
Promise.then
_request @ index-BXGQS6JU.js:23
request @ index-BXGQS6JU.js:22
(匿名) @ index-BXGQS6JU.js:23
(匿名) @ index-BXGQS6JU.js:18
g @ order-BLZdhLwT.js:1
te @ ProductOrderForm-C-g5v4bk.js:1
await in te
Yi @ index-BXGQS6JU.js:14
Wo @ index-BXGQS6JU.js:14
JT @ index-BXGQS6JU.js:14
handleClick @ index-BXGQS6JU.js:23
Yi @ index-BXGQS6JU.js:14
Wo @ index-BXGQS6JU.js:14
n @ index-BXGQS6JU.js:18
ProductOrderForm-C-g5v4bk.js:1 监听到产品信息变化: Proxy(Object) {id: 29, order_id: 43, product_id: 9, user_count: 1, account_count: 3, …}
ProductOrderForm-C-g5v4bk.js:1 更新后的订单金额: {standard_amount: '898.00', actual_amount: '898.00'}
ProductOrderForm-C-g5v4bk.js:1 监听到价格字段变化: {standardPrice: '898.00', actualPrice: '898.00'}
ProductOrderForm-C-g5v4bk.js:1 ProductOrderForm orderData 企业/用户ID变化: {enterprise_id: 11, user_id: null, enterprise_type: 'number', user_type: 'object'}
OrderStatusBadge-CPr9zilU.js:1 OrderHeader updateParent 被调用，当前数据: {enterprise_id: 11, user_id: null, is_partner_order: true, partner_user_id: 15, commission_base: null, …}
ProductOrderForm-C-g5v4bk.js:1 表头数据更新: {order_id: 'PO20250729BKN', order_category: '产品订单', enterprise_id: 11, asset_id: null, user_id: null, …}
ProductOrderForm-C-g5v4bk.js:1 监听到产品信息变化: Proxy(Object) {id: 29, order_id: 43, product_id: 9, user_count: 1, account_count: 3, …}
ProductOrderForm-C-g5v4bk.js:1 更新后的订单金额: {standard_amount: '898.00', actual_amount: '898.00'}
ProductOrderForm-C-g5v4bk.js:1 监听到价格字段变化: {standardPrice: '898.00', actualPrice: '898.00'}
ProductOrderForm-C-g5v4bk.js:1 ProductOrderForm orderData 企业/用户ID变化: {enterprise_id: 11, user_id: null, enterprise_type: 'number', user_type: 'object'}
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/orders/43"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-BXGQS6JU.js:20
xhr @ index-BXGQS6JU.js:20
Xg @ index-BXGQS6JU.js:22
Promise.then
_request @ index-BXGQS6JU.js:23
request @ index-BXGQS6JU.js:22
Ga.<computed> @ index-BXGQS6JU.js:23
(匿名) @ index-BXGQS6JU.js:18
O @ order-BLZdhLwT.js:1
b @ ProductOrderForm-C-g5v4bk.js:1
te @ ProductOrderForm-C-g5v4bk.js:1
await in te
Yi @ index-BXGQS6JU.js:14
Wo @ index-BXGQS6JU.js:14
JT @ index-BXGQS6JU.js:14
handleClick @ index-BXGQS6JU.js:23
Yi @ index-BXGQS6JU.js:14
Wo @ index-BXGQS6JU.js:14
n @ index-BXGQS6JU.js:18
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/orders/43/attachments"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-BXGQS6JU.js:20
xhr @ index-BXGQS6JU.js:20
Xg @ index-BXGQS6JU.js:22
Promise.then
_request @ index-BXGQS6JU.js:23
request @ index-BXGQS6JU.js:22
Ga.<computed> @ index-BXGQS6JU.js:23
(匿名) @ index-BXGQS6JU.js:18
h @ order-BLZdhLwT.js:1
K @ OrderStatusBadge-CPr9zilU.js:1
(匿名) @ OrderStatusBadge-CPr9zilU.js:1
(匿名) @ index-BXGQS6JU.js:14
Yi @ index-BXGQS6JU.js:14
Wo @ index-BXGQS6JU.js:14
t.__weh.t.__weh @ index-BXGQS6JU.js:14
A1 @ index-BXGQS6JU.js:14
L1 @ index-BXGQS6JU.js:14
Promise.then
P1 @ index-BXGQS6JU.js:14
jv @ index-BXGQS6JU.js:14
P.$e.scheduler @ index-BXGQS6JU.js:14
trigger @ index-BXGQS6JU.js:10
Bv @ index-BXGQS6JU.js:10
notify @ index-BXGQS6JU.js:10
trigger @ index-BXGQS6JU.js:10
set value @ index-BXGQS6JU.js:10
te @ ProductOrderForm-C-g5v4bk.js:1
await in te
Yi @ index-BXGQS6JU.js:14
Wo @ index-BXGQS6JU.js:14
JT @ index-BXGQS6JU.js:14
handleClick @ index-BXGQS6JU.js:23
Yi @ index-BXGQS6JU.js:14
Wo @ index-BXGQS6JU.js:14
n @ index-BXGQS6JU.js:18
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/orders/43/attachments"。