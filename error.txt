ServiceInfo组件接收到的数据: Proxy(Object) {order_id: '', order_category: '服务订单', enterprise_id: null, asset_id: null, user_id: null, …}
ServiceOrderForm-FSd3TJKE.js:1 服务明细数据: Proxy(Array) {}
ServiceOrderForm-FSd3TJKE.js:1 ServiceInfo组件接收到的数据: Proxy(Object) {service_items: Proxy(Array), standard_amount: 0, actual_amount: 0}
ServiceOrderForm-FSd3TJKE.js:1 服务明细数据: Proxy(Array) {0: {…}}
ServiceOrderForm-FSd3TJKE.js:1 ServiceOrderForm 组件挂载，当前模式: create 是否编辑: true
ServiceOrderForm-FSd3TJKE.js:1 初始化新服务订单
OrderStatusBadge-p2z5p-1u.js:1 订单编码生成成功: SO20250730J8K
OrderStatusBadge-p2z5p-1u.js:1 OrderHeader updateParent 被调用，当前数据: {enterprise_id: null, user_id: null, is_partner_order: false, partner_user_id: null, commission_base: null, …}
ServiceOrderForm-FSd3TJKE.js:1 ServiceInfo组件接收到的数据: Proxy(Object) {order_id: 'SO20250730J8K', order_category: '服务订单', enterprise_id: null, asset_id: null, user_id: null, …}
ServiceOrderForm-FSd3TJKE.js:1 服务明细数据: Proxy(Array) {0: {…}}
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/orders/next-id?orderType=service"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-CIP-BCK5.js:20
xhr @ index-CIP-BCK5.js:20
Xg @ index-CIP-BCK5.js:22
Promise.then
_request @ index-CIP-BCK5.js:23
request @ index-CIP-BCK5.js:22
Ga.<computed> @ index-CIP-BCK5.js:23
(匿名) @ index-CIP-BCK5.js:18
c @ order-CrlmbCou.js:1
ce @ OrderStatusBadge-p2z5p-1u.js:1
(匿名) @ OrderStatusBadge-p2z5p-1u.js:1
(匿名) @ index-CIP-BCK5.js:14
Yi @ index-CIP-BCK5.js:14
Wo @ index-CIP-BCK5.js:14
t.__weh.t.__weh @ index-CIP-BCK5.js:14
A1 @ index-CIP-BCK5.js:14
L1 @ index-CIP-BCK5.js:14
Promise.then
P1 @ index-CIP-BCK5.js:14
gT @ index-CIP-BCK5.js:14
n$ @ index-CIP-BCK5.js:14
Zv.i.scheduler @ index-CIP-BCK5.js:14
d.scheduler @ index-CIP-BCK5.js:10
trigger @ index-CIP-BCK5.js:10
Bv @ index-CIP-BCK5.js:10
notify @ index-CIP-BCK5.js:10
trigger @ index-CIP-BCK5.js:10
set value @ index-CIP-BCK5.js:10
M @ index-CIP-BCK5.js:63
(匿名) @ index-CIP-BCK5.js:63
Promise.then
O @ index-CIP-BCK5.js:63
w @ index-CIP-BCK5.js:63
install @ index-CIP-BCK5.js:63
use @ index-CIP-BCK5.js:14
(匿名) @ index-CIP-BCK5.js:63
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/enterprises"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-CIP-BCK5.js:20
xhr @ index-CIP-BCK5.js:20
Xg @ index-CIP-BCK5.js:22
Promise.then
_request @ index-CIP-BCK5.js:23
request @ index-CIP-BCK5.js:22
Ga.<computed> @ index-CIP-BCK5.js:23
(匿名) @ index-CIP-BCK5.js:18
vte @ index-CIP-BCK5.js:63
B @ OrderStatusBadge-p2z5p-1u.js:1
initialize @ OrderStatusBadge-p2z5p-1u.js:1
(匿名) @ OrderStatusBadge-p2z5p-1u.js:1
await in (匿名)
(匿名) @ index-CIP-BCK5.js:14
Yi @ index-CIP-BCK5.js:14
Wo @ index-CIP-BCK5.js:14
t.__weh.t.__weh @ index-CIP-BCK5.js:14
A1 @ index-CIP-BCK5.js:14
L1 @ index-CIP-BCK5.js:14
Promise.then
P1 @ index-CIP-BCK5.js:14
gT @ index-CIP-BCK5.js:14
n$ @ index-CIP-BCK5.js:14
Zv.i.scheduler @ index-CIP-BCK5.js:14
d.scheduler @ index-CIP-BCK5.js:10
trigger @ index-CIP-BCK5.js:10
Bv @ index-CIP-BCK5.js:10
notify @ index-CIP-BCK5.js:10
trigger @ index-CIP-BCK5.js:10
set value @ index-CIP-BCK5.js:10
M @ index-CIP-BCK5.js:63
(匿名) @ index-CIP-BCK5.js:63
Promise.then
O @ index-CIP-BCK5.js:63
w @ index-CIP-BCK5.js:63
install @ index-CIP-BCK5.js:63
use @ index-CIP-BCK5.js:14
(匿名) @ index-CIP-BCK5.js:63
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/users"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-CIP-BCK5.js:20
xhr @ index-CIP-BCK5.js:20
Xg @ index-CIP-BCK5.js:22
Promise.then
_request @ index-CIP-BCK5.js:23
request @ index-CIP-BCK5.js:22
Ga.<computed> @ index-CIP-BCK5.js:23
(匿名) @ index-CIP-BCK5.js:18
yte @ index-CIP-BCK5.js:63
O @ OrderStatusBadge-p2z5p-1u.js:1
initialize @ OrderStatusBadge-p2z5p-1u.js:1
(匿名) @ OrderStatusBadge-p2z5p-1u.js:1
await in (匿名)
(匿名) @ index-CIP-BCK5.js:14
Yi @ index-CIP-BCK5.js:14
Wo @ index-CIP-BCK5.js:14
t.__weh.t.__weh @ index-CIP-BCK5.js:14
A1 @ index-CIP-BCK5.js:14
L1 @ index-CIP-BCK5.js:14
Promise.then
P1 @ index-CIP-BCK5.js:14
gT @ index-CIP-BCK5.js:14
n$ @ index-CIP-BCK5.js:14
Zv.i.scheduler @ index-CIP-BCK5.js:14
d.scheduler @ index-CIP-BCK5.js:10
trigger @ index-CIP-BCK5.js:10
Bv @ index-CIP-BCK5.js:10
notify @ index-CIP-BCK5.js:10
trigger @ index-CIP-BCK5.js:10
set value @ index-CIP-BCK5.js:10
M @ index-CIP-BCK5.js:63
(匿名) @ index-CIP-BCK5.js:63
Promise.then
O @ index-CIP-BCK5.js:63
w @ index-CIP-BCK5.js:63
install @ index-CIP-BCK5.js:63
use @ index-CIP-BCK5.js:14
(匿名) @ index-CIP-BCK5.js:63
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/assets"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-CIP-BCK5.js:20
xhr @ index-CIP-BCK5.js:20
Xg @ index-CIP-BCK5.js:22
Promise.then
_request @ index-CIP-BCK5.js:23
request @ index-CIP-BCK5.js:22
(匿名) @ index-CIP-BCK5.js:18
d @ asset-CVGDDZB2.js:1
z @ OrderStatusBadge-p2z5p-1u.js:1
initialize @ OrderStatusBadge-p2z5p-1u.js:1
(匿名) @ OrderStatusBadge-p2z5p-1u.js:1
await in (匿名)
(匿名) @ index-CIP-BCK5.js:14
Yi @ index-CIP-BCK5.js:14
Wo @ index-CIP-BCK5.js:14
t.__weh.t.__weh @ index-CIP-BCK5.js:14
A1 @ index-CIP-BCK5.js:14
L1 @ index-CIP-BCK5.js:14
Promise.then
P1 @ index-CIP-BCK5.js:14
gT @ index-CIP-BCK5.js:14
n$ @ index-CIP-BCK5.js:14
Zv.i.scheduler @ index-CIP-BCK5.js:14
d.scheduler @ index-CIP-BCK5.js:10
trigger @ index-CIP-BCK5.js:10
Bv @ index-CIP-BCK5.js:10
notify @ index-CIP-BCK5.js:10
trigger @ index-CIP-BCK5.js:10
set value @ index-CIP-BCK5.js:10
M @ index-CIP-BCK5.js:63
(匿名) @ index-CIP-BCK5.js:63
Promise.then
O @ index-CIP-BCK5.js:63
w @ index-CIP-BCK5.js:63
install @ index-CIP-BCK5.js:63
use @ index-CIP-BCK5.js:14
(匿名) @ index-CIP-BCK5.js:63
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/enterprises"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-CIP-BCK5.js:20
xhr @ index-CIP-BCK5.js:20
Xg @ index-CIP-BCK5.js:22
Promise.then
_request @ index-CIP-BCK5.js:23
request @ index-CIP-BCK5.js:22
Ga.<computed> @ index-CIP-BCK5.js:23
(匿名) @ index-CIP-BCK5.js:18
vte @ index-CIP-BCK5.js:63
B @ OrderStatusBadge-p2z5p-1u.js:1
initialize @ OrderStatusBadge-p2z5p-1u.js:1
Yi @ index-CIP-BCK5.js:14
Wo @ index-CIP-BCK5.js:14
JT @ index-CIP-BCK5.js:14
d @ index-CIP-BCK5.js:23
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/users"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-CIP-BCK5.js:20
xhr @ index-CIP-BCK5.js:20
Xg @ index-CIP-BCK5.js:22
Promise.then
_request @ index-CIP-BCK5.js:23
request @ index-CIP-BCK5.js:22
Ga.<computed> @ index-CIP-BCK5.js:23
(匿名) @ index-CIP-BCK5.js:18
yte @ index-CIP-BCK5.js:63
O @ OrderStatusBadge-p2z5p-1u.js:1
initialize @ OrderStatusBadge-p2z5p-1u.js:1
Yi @ index-CIP-BCK5.js:14
Wo @ index-CIP-BCK5.js:14
JT @ index-CIP-BCK5.js:14
d @ index-CIP-BCK5.js:23
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/assets"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-CIP-BCK5.js:20
xhr @ index-CIP-BCK5.js:20
Xg @ index-CIP-BCK5.js:22
Promise.then
_request @ index-CIP-BCK5.js:23
request @ index-CIP-BCK5.js:22
(匿名) @ index-CIP-BCK5.js:18
d @ asset-CVGDDZB2.js:1
z @ OrderStatusBadge-p2z5p-1u.js:1
initialize @ OrderStatusBadge-p2z5p-1u.js:1
Yi @ index-CIP-BCK5.js:14
Wo @ index-CIP-BCK5.js:14
JT @ index-CIP-BCK5.js:14
d @ index-CIP-BCK5.js:23
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/enterprises"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-CIP-BCK5.js:20
xhr @ index-CIP-BCK5.js:20
Xg @ index-CIP-BCK5.js:22
Promise.then
_request @ index-CIP-BCK5.js:23
request @ index-CIP-BCK5.js:22
Ga.<computed> @ index-CIP-BCK5.js:23
(匿名) @ index-CIP-BCK5.js:18
vte @ index-CIP-BCK5.js:63
B @ OrderStatusBadge-p2z5p-1u.js:1
searchEnterprises @ OrderStatusBadge-p2z5p-1u.js:1
Ce @ index-CIP-BCK5.js:40
(匿名) @ index-CIP-BCK5.js:40
Yi @ index-CIP-BCK5.js:14
Wo @ index-CIP-BCK5.js:14
Zv.i.call @ index-CIP-BCK5.js:14
b @ index-CIP-BCK5.js:10
Yi @ index-CIP-BCK5.js:14
L1 @ index-CIP-BCK5.js:14
Promise.then
P1 @ index-CIP-BCK5.js:14
jv @ index-CIP-BCK5.js:14
Zv.i.scheduler @ index-CIP-BCK5.js:14
d.scheduler @ index-CIP-BCK5.js:10
trigger @ index-CIP-BCK5.js:10
Bv @ index-CIP-BCK5.js:10
notify @ index-CIP-BCK5.js:10
trigger @ index-CIP-BCK5.js:10
set value @ index-CIP-BCK5.js:10
kt @ index-CIP-BCK5.js:40
Ge.n.<computed>.n.<computed> @ index-CIP-BCK5.js:18
Yi @ index-CIP-BCK5.js:14
Wo @ index-CIP-BCK5.js:14
n @ index-CIP-BCK5.js:18
OrderStatusBadge-p2z5p-1u.js:1 企业变化处理: {原始值: 2, 清理后: 2, 类型: 'number'}
OrderStatusBadge-p2z5p-1u.js:1 OrderHeader updateParent 被调用，当前数据: {enterprise_id: 2, user_id: null, is_partner_order: false, partner_user_id: null, commission_base: null, …}
ServiceOrderForm-FSd3TJKE.js:1 ServiceInfo组件接收到的数据: Proxy(Object) {order_id: 'SO20250730J8K', order_category: '服务订单', enterprise_id: 2, asset_id: null, user_id: null, …}
ServiceOrderForm-FSd3TJKE.js:1 服务明细数据: Proxy(Array) {0: {…}}
OrderStatusBadge-p2z5p-1u.js:1 企业字段验证: {value: 2, enterpriseId: 2, userId: null, enterpriseType: 'number', userType: 'object'}
OrderStatusBadge-p2z5p-1u.js:1 OrderHeader updateParent 被调用，当前数据: {enterprise_id: 2, user_id: 2, is_partner_order: false, partner_user_id: null, commission_base: null, …}
ServiceOrderForm-FSd3TJKE.js:1 ServiceInfo组件接收到的数据: Proxy(Object) {order_id: 'SO20250730J8K', order_category: '服务订单', enterprise_id: 2, asset_id: null, user_id: 2, …}
ServiceOrderForm-FSd3TJKE.js:1 服务明细数据: Proxy(Array) {0: {…}}
OrderStatusBadge-p2z5p-1u.js:1 用户字段验证: {value: 2, userId: 2, enterpriseId: 2, userType: 'number', enterpriseType: 'number'}
OrderStatusBadge-p2z5p-1u.js:1 用户字段验证: {value: 2, userId: 2, enterpriseId: 2, userType: 'number', enterpriseType: 'number'}
OrderStatusBadge-p2z5p-1u.js:1 企业字段验证: {value: 2, enterpriseId: 2, userId: 2, enterpriseType: 'number', userType: 'number'}
OrderStatusBadge-p2z5p-1u.js:1 OrderHeader updateParent 被调用，当前数据: {enterprise_id: 2, user_id: 2, is_partner_order: false, partner_user_id: null, commission_base: null, …}
ServiceOrderForm-FSd3TJKE.js:1 ServiceInfo组件接收到的数据: Proxy(Object) {order_id: 'SO20250730J8K', order_category: '服务订单', enterprise_id: 2, asset_id: null, user_id: 2, …}
ServiceOrderForm-FSd3TJKE.js:1 服务明细数据: Proxy(Array) {0: {…}}
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/assets?enterprise_id=2"。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-CIP-BCK5.js:20
xhr @ index-CIP-BCK5.js:20
Xg @ index-CIP-BCK5.js:22
Promise.then
_request @ index-CIP-BCK5.js:23
request @ index-CIP-BCK5.js:22
(匿名) @ index-CIP-BCK5.js:18
d @ asset-CVGDDZB2.js:1
z @ OrderStatusBadge-p2z5p-1u.js:1
handleEnterpriseChange @ OrderStatusBadge-p2z5p-1u.js:1
_e @ OrderStatusBadge-p2z5p-1u.js:1
Yi @ index-CIP-BCK5.js:14
Wo @ index-CIP-BCK5.js:14
JT @ index-CIP-BCK5.js:14
mt @ index-CIP-BCK5.js:40
ye @ index-CIP-BCK5.js:40
m @ index-CIP-BCK5.js:40
Ge.n.<computed>.n.<computed> @ index-CIP-BCK5.js:18
Yi @ index-CIP-BCK5.js:14
Wo @ index-CIP-BCK5.js:14
n @ index-CIP-BCK5.js:18
OrderStatusBadge-p2z5p-1u.js:1 企业字段验证: {value: 2, enterpriseId: 2, userId: 2, enterpriseType: 'number', userType: 'number'}
OrderStatusBadge-p2z5p-1u.js:1 OrderHeader updateParent 被调用，当前数据: {enterprise_id: 2, user_id: 2, is_partner_order: true, partner_user_id: null, commission_base: null, …}
ServiceOrderForm-FSd3TJKE.js:1 ServiceInfo组件接收到的数据: Proxy(Object) {order_id: 'SO20250730J8K', order_category: '服务订单', enterprise_id: 2, asset_id: null, user_id: 2, …}
ServiceOrderForm-FSd3TJKE.js:1 服务明细数据: Proxy(Array) {0: {…}}
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/users?q="。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-CIP-BCK5.js:20
xhr @ index-CIP-BCK5.js:20
Xg @ index-CIP-BCK5.js:22
Promise.then
_request @ index-CIP-BCK5.js:23
request @ index-CIP-BCK5.js:22
Ga.<computed> @ index-CIP-BCK5.js:23
(匿名) @ index-CIP-BCK5.js:18
yte @ index-CIP-BCK5.js:63
te @ OrderStatusBadge-p2z5p-1u.js:1
J @ OrderStatusBadge-p2z5p-1u.js:1
Yi @ index-CIP-BCK5.js:14
Wo @ index-CIP-BCK5.js:14
JT @ index-CIP-BCK5.js:14
d @ index-CIP-BCK5.js:23
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/users?q="。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-CIP-BCK5.js:20
xhr @ index-CIP-BCK5.js:20
Xg @ index-CIP-BCK5.js:22
Promise.then
_request @ index-CIP-BCK5.js:23
request @ index-CIP-BCK5.js:22
Ga.<computed> @ index-CIP-BCK5.js:23
(匿名) @ index-CIP-BCK5.js:18
yte @ index-CIP-BCK5.js:63
te @ OrderStatusBadge-p2z5p-1u.js:1
J @ OrderStatusBadge-p2z5p-1u.js:1
Yi @ index-CIP-BCK5.js:14
Wo @ index-CIP-BCK5.js:14
JT @ index-CIP-BCK5.js:14
d @ index-CIP-BCK5.js:23
inspector.js:7 XHR 已完成加载:GET "https://service.bogoo.net/api/users?q="。
XMLHttpRequest.send @ inspector.js:7
(匿名) @ index-CIP-BCK5.js:20
xhr @ index-CIP-BCK5.js:20
Xg @ index-CIP-BCK5.js:22
Promise.then
_request @ index-CIP-BCK5.js:23
request @ index-CIP-BCK5.js:22
Ga.<computed> @ index-CIP-BCK5.js:23
(匿名) @ index-CIP-BCK5.js:18
yte @ index-CIP-BCK5.js:63
te @ OrderStatusBadge-p2z5p-1u.js:1
Ce @ index-CIP-BCK5.js:40
(匿名) @ index-CIP-BCK5.js:40
Yi @ index-CIP-BCK5.js:14
Wo @ index-CIP-BCK5.js:14
Zv.i.call @ index-CIP-BCK5.js:14
b @ index-CIP-BCK5.js:10
Yi @ index-CIP-BCK5.js:14
L1 @ index-CIP-BCK5.js:14
Promise.then
P1 @ index-CIP-BCK5.js:14
jv @ index-CIP-BCK5.js:14
Zv.i.scheduler @ index-CIP-BCK5.js:14
d.scheduler @ index-CIP-BCK5.js:10
trigger @ index-CIP-BCK5.js:10
Bv @ index-CIP-BCK5.js:10
notify @ index-CIP-BCK5.js:10
trigger @ index-CIP-BCK5.js:10
set value @ index-CIP-BCK5.js:10
kt @ index-CIP-BCK5.js:40
Ge.n.<computed>.n.<computed> @ index-CIP-BCK5.js:18
Yi @ index-CIP-BCK5.js:14
Wo @ index-CIP-BCK5.js:14
n @ index-CIP-BCK5.js:18
OrderStatusBadge-p2z5p-1u.js:1 加载合伙人信息，ID: 15
OrderStatusBadge-p2z5p-1u.js:1 合伙人已在列表中: 张小草
OrderStatusBadge-p2z5p-1u.js:1 OrderHeader updateParent 被调用，当前数据: {enterprise_id: 2, user_id: 2, is_partner_order: true, partner_user_id: 15, commission_base: '0.2000', …}
OrderStatusBadge-p2z5p-1u.js:1 加载合伙人信息，ID: 15
OrderStatusBadge-p2z5p-1u.js:1 合伙人已在列表中: 张小草
ServiceOrderForm-FSd3TJKE.js:1 ServiceInfo组件接收到的数据: Proxy(Object) {order_id: 'SO20250730J8K', order_category: '服务订单', enterprise_id: 2, asset_id: null, user_id: 2, …}
ServiceOrderForm-FSd3TJKE.js:1 服务明细数据: Proxy(Array) {0: {…}}
OrderStatusBadge-p2z5p-1u.js:1 OrderHeader updateParent 被调用，当前数据: {enterprise_id: 2, user_id: 2, is_partner_order: true, partner_user_id: 15, commission_base: '0.2000', …}
ServiceOrderForm-FSd3TJKE.js:1 ServiceInfo组件接收到的数据: Proxy(Object) {order_id: 'SO20250730J8K', order_category: '服务订单', enterprise_id: 2, asset_id: null, user_id: 2, …}
ServiceOrderForm-FSd3TJKE.js:1 服务明细数据: Proxy(Array) {0: {…}}
OrderStatusBadge-p2z5p-1u.js:1 OrderHeader updateParent 被调用，当前数据: {enterprise_id: 2, user_id: 2, is_partner_order: true, partner_user_id: 15, commission_base: '0.2000', …}
OrderStatusBadge-p2z5p-1u.js:1 OrderHeader updateParent 被调用，当前数据: {enterprise_id: 2, user_id: 2, is_partner_order: true, partner_user_id: 15, commission_base: '0.2000', …}
ServiceOrderForm-FSd3TJKE.js:1 ServiceInfo组件接收到的数据: Proxy(Object) {order_id: 'SO20250730J8K', order_category: '服务订单', enterprise_id: 2, asset_id: null, user_id: 2, …}
ServiceOrderForm-FSd3TJKE.js:1 服务明细数据: Proxy(Array) {0: {…}}
ServiceOrderForm-FSd3TJKE.js:1 ServiceInfo组件接收到的数据: Proxy(Object) {service_items: Proxy(Array), standard_amount: 5000, actual_amount: 5000}
ServiceOrderForm-FSd3TJKE.js:1 服务明细数据: Proxy(Array) {0: {…}}
OrderStatusBadge-p2z5p-1u.js:1 自动计算分润金额: {baseRatio: 0.2, extraRatio: 0.1, totalRatio: 0.30000000000000004, actualAmount: 5000, commissionAmount: 1500}
OrderStatusBadge-p2z5p-1u.js:1 OrderHeader updateParent 被调用，当前数据: {enterprise_id: 2, user_id: 2, is_partner_order: true, partner_user_id: 15, commission_base: '0.2000', …}
ServiceOrderForm-FSd3TJKE.js:1 ServiceInfo组件接收到的数据: Proxy(Object) {order_id: 'SO20250730J8K', order_category: '服务订单', enterprise_id: 2, asset_id: null, user_id: 2, …}
ServiceOrderForm-FSd3TJKE.js:1 服务明细数据: Proxy(Array) {0: {…}}
ServiceOrderForm-FSd3TJKE.js:1 ServiceInfo组件接收到的数据: Proxy(Object) {service_items: Proxy(Array), standard_amount: 5000, actual_amount: 5000.01}
ServiceOrderForm-FSd3TJKE.js:1 服务明细数据: Proxy(Array) {0: {…}}
ServiceOrderForm-FSd3TJKE.js:1 ServiceInfo组件接收到的数据: Proxy(Object) {service_items: Proxy(Array), standard_amount: 5000, actual_amount: 5000.02}
ServiceOrderForm-FSd3TJKE.js:1 服务明细数据: Proxy(Array) {0: {…}}
OrderStatusBadge-p2z5p-1u.js:1 自动计算分润金额: {baseRatio: 0.2, extraRatio: 0.1, totalRatio: 0.30000000000000004, actualAmount: 5000.02, commissionAmount: 1500.01}
OrderStatusBadge-p2z5p-1u.js:1 OrderHeader updateParent 被调用，当前数据: {enterprise_id: 2, user_id: 2, is_partner_order: true, partner_user_id: 15, commission_base: '0.2000', …}
ServiceOrderForm-FSd3TJKE.js:1 ServiceInfo组件接收到的数据: Proxy(Object) {order_id: 'SO20250730J8K', order_category: '服务订单', enterprise_id: 2, asset_id: null, user_id: 2, …}
ServiceOrderForm-FSd3TJKE.js:1 服务明细数据: Proxy(Array) {0: {…}}
ServiceOrderForm-FSd3TJKE.js:1 ServiceInfo组件接收到的数据: Proxy(Object) {service_items: Proxy(Array), standard_amount: 5000, actual_amount: 5000.03}
ServiceOrderForm-FSd3TJKE.js:1 服务明细数据: Proxy(Array) {0: {…}}
ServiceOrderForm-FSd3TJKE.js:1 ServiceInfo组件接收到的数据: Proxy(Object) {service_items: Proxy(Array), standard_amount: 5000, actual_amount: 5000.04}
ServiceOrderForm-FSd3TJKE.js:1 服务明细数据: Proxy(Array) {0: {…}}
OrderStatusBadge-p2z5p-1u.js:1 企业字段验证: {value: 2, enterpriseId: 2, userId: 2, enterpriseType: 'number', userType: 'number'}
OrderStatusBadge-p2z5p-1u.js:1 用户字段验证: {value: 2, userId: 2, enterpriseId: 2, userType: 'number', enterpriseType: 'number'}